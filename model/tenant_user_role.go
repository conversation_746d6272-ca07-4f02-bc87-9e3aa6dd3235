package model

import (
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
)

const (
	TenantRoleAdmin   = "admin"
	TenantRoleTeacher = "teacher"
)

type TenantUserRole struct {
	gorm.Model

	UUID         uuid.UUID `gorm:"uniqueIndex;type:uuid;default:uuid_generate_v4()"`
	TenantUserID uuid.UUID `gorm:"index;type:uuid"` // 关联的租户用户UUID
	RoleID       uuid.UUID `gorm:"index;type:uuid"` // 关联的角色UUID

	TenantID uuid.UUID `gorm:"index;type:uuid"` // 关联的租户UUID
	UserID   uuid.UUID `gorm:"index;type:uuid"` // 关联的用户UUID
}

func (tur *TenantUserRole) TableName() string {
	return "tenant_user_roles"
}

// Create creates a new tenant user role in the database
func CreateTenantUserRole(db *gorm.DB, tenantUserRole *TenantUserRole) error {
	return db.Create(tenantUserRole).Error
}

// GetByUUID retrieves a tenant user role by UUID
func GetTenantUserRoleByUUID(db *gorm.DB, id uuid.UUID) (*TenantUserRole, error) {
	var tenantUserRole TenantUserRole
	err := db.Where("uuid = ?", id).First(tenantUserRole).Error
	if err != nil {
		return nil, err
	}
	return &tenantUserRole, nil
}

// GetByTenantUserAndRole retrieves a tenant user role by tenant user ID
func GetTenantUserRoleByTenantUserID(db *gorm.DB, tenantUserID uuid.UUID) (*TenantUserRole, error) {
	var tenantUserRole TenantUserRole
	err := db.Where("tenant_user_id = ?", tenantUserID).First(tenantUserRole).Error
	if err != nil {
		return nil, err
	}
	return &tenantUserRole, nil
}

type TenantRoleDetail struct {
	TenantUserRoleID uuid.UUID `gorm:"tenant_user_role_id"`
	TenantUserID     uuid.UUID `gorm:"tenant_user_id"`
	TenantID         uuid.UUID `gorm:"tenant_id"`
	TenantName       string    `gorm:"tenant_name"`
	UserID           uuid.UUID `gorm:"user_id"`
	RoleID           uuid.UUID `gorm:"role_id"`
	RoleCode         string    `gorm:"role_code"`
	RoleName         string    `gorm:"role_name"`
}

// GetByTenantIDAndUserID retrieves tenant user roles by tenant ID and user ID
func GetTenantUserRoleByTenantIDAndUserID(db *gorm.DB, tenantID, userID string) ([]TenantRoleDetail, error) {
	var roles []TenantRoleDetail

	query := `
			SELECT 
					tur.uuid AS tenant_user_role_id,
					tur.tenant_user_id,
					tur.tenant_id,
					t.name AS tenant_name,
					tur.user_id,
					tur.role_id,
					r.code AS role_code,
					r.name AS role_name
			FROM tenant_user_roles tur
			LEFT JOIN roles r ON tur.role_id = r.uuid
			LEFT JOIN tenants t ON tur.tenant_id = t.uuid
			WHERE tur.tenant_id = ? AND tur.user_id = ?
	`

	err := db.Raw(query, tenantID, userID).Scan(&roles).Error
	return roles, err
}

func GetTenantUserRoleByUserID(db *gorm.DB, userID string) ([]TenantRoleDetail, error) {
	var roles []TenantRoleDetail

	query := `
			SELECT 
					tur.uuid as tenant_user_role_id,
					tur.tenant_user_id,
					tur.tenant_id,
					t.name as tenant_name,
					tur.user_id,
					tur.role_id,
					r.code as role_code,
					r.name as role_name
			FROM tenant_user_roles tur
			LEFT JOIN roles r ON tur.role_id = r.uuid
			LEFT JOIN tenants t ON tur.tenant_id = t.uuid
			WHERE tur.user_id = ?
	`

	err := db.Raw(query, userID).Scan(&roles).Error
	return roles, err
}

// Update updates an existing tenant user role
func UpdateTenantUserRole(db *gorm.DB, tenantUserRole *TenantUserRole) error {
	return db.Save(tenantUserRole).Error
}

// Delete removes a tenant user role from the database
func DeleteTenantUserRole(db *gorm.DB, tenantUserRole *TenantUserRole) error {
	return db.Delete(tenantUserRole).Error
}

func DeleteTenantUserRolesByTenantUserID(db *gorm.DB, tenantUserID string) error {
	return db.Where("tenant_user_id = ?", tenantUserID).Delete(&TenantUserRole{}).Error
}
