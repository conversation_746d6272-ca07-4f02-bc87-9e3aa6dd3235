package model

import (
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
)

type Question struct {
	gorm.Model

	UUID     uuid.UUID `gorm:"uniqueIndex;type:uuid;default:uuid_generate_v4()"`
	TenantID uuid.UUID `gorm:"type:uuid;index"` // 租户ID

	Grade   string `gorm:"type:varchar(64);index"` // 年级
	Subject string `gorm:"type:varchar(64);index"` // 科目
	Term    string `gorm:"type:varchar(64);index"` // 学期

	Question         string
	QuestionImage    string
	Answer           string
	AnswerImage      string
	Explanation      string
	ExplanationImage string
}

func (q *Question) TableName() string {
	return "questions"
}

// CreateQuestion creates a new question in the database
func CreateQuestion(db *gorm.DB, question *Question) error {
	return db.Create(question).Error
}

// GetQuestionByID retrieves a question by its ID
func GetQuestionByID(db *gorm.DB, id uint) (*Question, error) {
	var question Question
	err := db.First(&question, id).Error
	return &question, err
}

// GetQuestionByUUID retrieves a question by its UUID
func GetQuestionByUUID(db *gorm.DB, uuid string) (*Question, error) {
	var question Question
	err := db.Where("uuid = ?", uuid).First(&question).Error
	return &question, err
}

// GetAllQuestions retrieves all questions with optional pagination and filters
func GetAllQuestions(db *gorm.DB, tenantID uuid.UUID, filters map[string]string, offset, limit int) ([]Question, int64, error) {
	var questions []Question
	var count int64

	query := db.Model(&Question{}).Where("tenant_id = ?", tenantID)

	// Apply filters if provided
	if grade, ok := filters["grade"]; ok && grade != "" {
		query = query.Where("grade = ?", grade)
	}
	if subject, ok := filters["subject"]; ok && subject != "" {
		query = query.Where("subject = ?", subject)
	}
	if term, ok := filters["term"]; ok && term != "" {
		query = query.Where("term = ?", term)
	}
	if search, ok := filters["search"]; ok && search != "" {
		query = query.Where("question LIKE ? OR answer LIKE ? OR explanation LIKE ?",
			"%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	if err := query.Count(&count).Error; err != nil {
		return nil, 0, err
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	if offset > 0 {
		query = query.Offset(offset)
	}

	// Default sorting by creation date, newest first
	query = query.Order("created_at DESC")

	if err := query.Find(&questions).Error; err != nil {
		return nil, 0, err
	}

	return questions, count, nil
}

// UpdateQuestion updates an existing question
func UpdateQuestion(db *gorm.DB, question *Question) error {
	return db.Save(question).Error
}

// DeleteQuestion deletes a question by its ID
func DeleteQuestion(db *gorm.DB, id uint) error {
	return db.Delete(&Question{}, id).Error
}

// DeleteQuestionByUUID deletes a question by its UUID
func DeleteQuestionByUUID(db *gorm.DB, uuid string) error {
	return db.Where("uuid = ?", uuid).Delete(&Question{}).Error
}

// GetQuestionsByExamination retrieves all questions associated with a specific examination
func GetQuestionsByExamination(db *gorm.DB, examinationID uuid.UUID) ([]Question, error) {
	var questions []Question
	err := db.Table("questions").
		Joins("JOIN examination_questions eq ON questions.uuid = eq.question_id::uuid").
		Where("eq.examination_id = ?", examinationID).
		Order("eq.number, eq.sub_number").
		Find(&questions).Error
	return questions, err
}

// GetQuestionsWithPagination retrieves questions with pagination, filtering and searching
func GetQuestionsWithPagination(db *gorm.DB, tenantID uuid.UUID, grade, subject, term, search string, page, pageSize int) ([]Question, int64, error) {
	var questions []Question
	var count int64

	offset := (page - 1) * pageSize

	query := db.Model(&Question{}).Where("tenant_id = ?", tenantID)

	if grade != "" {
		query = query.Where("grade = ?", grade)
	}

	if subject != "" {
		query = query.Where("subject = ?", subject)
	}

	if term != "" {
		query = query.Where("term = ?", term)
	}

	if search != "" {
		query = query.Where("question LIKE ? OR answer LIKE ? OR explanation LIKE ?",
			"%"+search+"%", "%"+search+"%", "%"+search+"%")
	}

	if err := query.Count(&count).Error; err != nil {
		return nil, 0, err
	}

	if err := query.Offset(offset).Limit(pageSize).Order("created_at DESC").Find(&questions).Error; err != nil {
		return nil, 0, err
	}

	return questions, count, nil
}

// BulkCreateQuestions creates multiple questions in a single transaction
func BulkCreateQuestions(db *gorm.DB, questions []Question) error {
	return db.Create(&questions).Error
}
