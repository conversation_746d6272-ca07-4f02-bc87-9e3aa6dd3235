package model

import (
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
)

type ExaminationQuestion struct {
	gorm.Model

	ExaminationID uuid.UUID `gorm:"type:uuid;index"`
	QuestionID    uuid.UUID `gorm:"type:uuid;index"`
	Number        string
	SubNumber     string
}

func (eq *ExaminationQuestion) TableName() string {
	return "examination_questions"
}

// CreateExaminationQuestion creates a new examination question
func CreateExaminationQuestion(db *gorm.DB, eq *ExaminationQuestion) error {
	return db.Create(eq).Error
}

// GetExaminationQuestionByID retrieves an examination question by its ID
func GetExaminationQuestionByID(db *gorm.DB, id uint) (*ExaminationQuestion, error) {
	var eq ExaminationQuestion
	err := db.First(&eq, id).Error
	return &eq, err
}

// GetExaminationQuestionByExamAndQuestion retrieves an examination question by examination ID and question ID
func GetExaminationQuestionByExamAndQuestion(db *gorm.DB, examinationID, questionID uuid.UUID) (*ExaminationQuestion, error) {
	var eq ExaminationQuestion
	err := db.Where("examination_id = ? AND question_id = ?", examinationID, questionID).First(&eq).Error
	return &eq, err
}

// GetQuestionsByExaminationID retrieves all questions for a specific examination
func GetQuestionsByExaminationID(db *gorm.DB, examinationID uuid.UUID) ([]ExaminationQuestion, error) {
	var eqs []ExaminationQuestion
	err := db.Where("examination_id = ?", examinationID).Order("number, sub_number").Find(&eqs).Error
	return eqs, err
}

// GetExaminationsByQuestionID retrieves all examinations that contain a specific question
func GetExaminationsByQuestionID(db *gorm.DB, questionID uuid.UUID) ([]ExaminationQuestion, error) {
	var eqs []ExaminationQuestion
	err := db.Where("question_id = ?", questionID).Find(&eqs).Error
	return eqs, err
}

// UpdateExaminationQuestion updates an existing examination question
func UpdateExaminationQuestion(db *gorm.DB, eq *ExaminationQuestion) error {
	return db.Save(eq).Error
}

// DeleteExaminationQuestion deletes an examination question by its ID
func DeleteExaminationQuestion(db *gorm.DB, id uint) error {
	return db.Delete(&ExaminationQuestion{}, id).Error
}

// DeleteExaminationQuestionByExamAndQuestion deletes an examination question by examination ID and question ID
func DeleteExaminationQuestionByExamAndQuestion(db *gorm.DB, examinationID, questionID uuid.UUID) error {
	return db.Where("examination_id = ? AND question_id = ?", examinationID, questionID).Delete(&ExaminationQuestion{}).Error
}

// DeleteAllQuestionsByExaminationID removes all questions associated with an examination
func DeleteAllQuestionsByExaminationID(db *gorm.DB, examinationID uuid.UUID) error {
	return db.Where("examination_id = ?", examinationID).Delete(&ExaminationQuestion{}).Error
}

// BulkCreateExaminationQuestions inserts multiple examination questions in a single transaction
func BulkCreateExaminationQuestions(db *gorm.DB, eqs []ExaminationQuestion) error {
	return db.Create(&eqs).Error
}
