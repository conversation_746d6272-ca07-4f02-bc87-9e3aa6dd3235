package model

import (
	"codeup.aliyun.com/level-up/public/common/utils"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
	"gorm.io/gorm/clause"
)

// UploadQueue 上传队列
type UploadQueue struct {
	gorm.Model

	// 基础信息
	UUID   uuid.UUID `gorm:"uniqueIndex;type:uuid;default:uuid_generate_v4()"`
	TaskID uuid.UUID `gorm:"index;type:uuid"` // 关联的批改任务ID
}

// UploadQueueItem 上传队列项
type UploadQueueItem struct {
	gorm.Model

	// 基础信息
	UUID         uuid.UUID `gorm:"uniqueIndex;type:uuid;default:uuid_generate_v4()"`
	QueueID      uuid.UUID `gorm:"index;type:uuid"`   // 关联的上传队列ID
	FileID       string    `gorm:"type:varchar(255)"` // 文件ID
	Sequence     int       `gorm:"default:0"`         // 序号，确保有序
	AnswerFileID string    `gorm:"type:varchar(255)"`
	MarkedFileID string    `gorm:"type:varchar(255)"` // 标记后的图片文件ID
}

// TableName 返回表名
func (q *UploadQueue) TableName() string {
	return "upload_queues"
}

// TableName 返回表名
func (i *UploadQueueItem) TableName() string {
	return "upload_queue_items"
}

// CreateUploadQueue 创建上传队列
func CreateUploadQueue(tx *gorm.DB, queue *UploadQueue) error {
	return tx.Create(queue).Error
}

// GetUploadQueue 获取上传队列
func GetUploadQueue(tx *gorm.DB, queueID string) (*UploadQueue, error) {
	var queue UploadQueue
	if err := tx.First(&queue, "uuid = ?", queueID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &queue, nil
}

// UpdateUploadQueue 更新上传队列
func UpdateUploadQueue(tx *gorm.DB, queueID string, updateMap map[string]interface{}) error {
	return tx.Model(&UploadQueue{}).Where("uuid = ?", queueID).Updates(updateMap).Error
}

// FindHasUnfinishedUploadQueueByShard 根据ID分片查找未完成的上传队列
// maxShard: 总分片数
// wantedShard: 要查询的分片编号(0-based)
// limit: 最大返回数量
func FindHasUnfinishedUploadQueueByShard(tx *gorm.DB, maxShard, wantedShard int, limit int) ([]*UploadQueue, error) {
	var queueUUIDs []uuid.UUID
	var queues []*UploadQueue

	// 第一步：获取符合条件的 UUID 列表
	subQuery := tx.Model(&UploadQueue{}).
		Select("upload_queues.uuid").
		Joins("LEFT JOIN upload_queue_items ON upload_queues.uuid = upload_queue_items.queue_id").
		Where("upload_queue_items.status in ?", []string{"", CorrectionSheetStatusWaiting}).
		Where("upload_queues.id % ? = ?", maxShard, wantedShard).
		Group("upload_queues.uuid").
		Order("MIN(upload_queues.created_at) ASC").
		Limit(limit)

	if err := subQuery.Find(&queueUUIDs).Error; err != nil {
		return nil, err
	}

	// 第二步：根据 UUID 列表获取完整记录
	if len(queueUUIDs) > 0 {
		if err := tx.Where("uuid IN ?", queueUUIDs).
			Order("created_at ASC").
			Find(&queues).Error; err != nil {
			return nil, err
		}
	}

	return queues, nil
}

// CreateUploadQueueItem 创建上传队列项
func CreateUploadQueueItem(tx *gorm.DB, item *UploadQueueItem) error {
	return tx.Create(item).Error
}

// GetUploadQueueItems 获取上传队列的所有项目
func GetUploadQueueItems(tx *gorm.DB, queueID string) ([]*UploadQueueItem, error) {
	var items []*UploadQueueItem
	if err := tx.Where("queue_id = ?", queueID).Order("sequence ASC").Find(&items).Error; err != nil {
		return nil, err
	}
	return items, nil
}

func GetUploadQueueItemsForUpdate(tx *gorm.DB, queueID string, limit int) ([]*UploadQueueItem, error) {
	var items []*UploadQueueItem
	if err := tx.Clauses(clause.Locking{Strength: "UPDATE"}).
		Where("queue_id = ?", queueID).
		Where("status in ?", []string{"", CorrectionSheetStatusWaiting}).
		Order("created_at ASC").
		Limit(limit).
		Find(&items).Error; err != nil {
		return nil, err
	}
	return items, nil
}

func GetUploadQueueItemsByIDs(tx *gorm.DB, itemIDs []string) ([]*UploadQueueItem, error) {
	var items []*UploadQueueItem
	if err := tx.Where("uuid IN ?", itemIDs).Find(&items).Error; err != nil {
		return nil, err
	}
	return items, nil
}

func GetUploadQueueItemsOfTask(tx *gorm.DB, taskID string, limit, offset int, needTotal bool) ([]*UploadQueueItem, int64, error) {
	var items []*UploadQueueItem
	var total int64 = 0

	query := tx.Table("upload_queue_items").
		Joins("LEFT JOIN upload_queues ON upload_queue_items.queue_id = upload_queues.uuid").
		Where("upload_queues.task_id = ?", taskID)

	if needTotal {
		if err := query.Count(&total).Error; err != nil {
			return nil, 0, err
		}
		if total == 0 {
			return nil, 0, nil
		}
	}

	if err := query.
		Order("upload_queues.created_at ASC").
		Order("ABS(upload_queue_items.sequence) ASC").
		Order("upload_queue_items.sequence DESC").
		Limit(limit).
		Offset(offset).
		Find(&items).Error; err != nil {
		return nil, 0, err
	}

	return items, total, nil
}

func BatchUpdateUploadQueueItemsStatus(tx *gorm.DB, itemIDs []string, status string) error {
	return BatchUpdateUploadQueueItems(tx, itemIDs, map[string]interface{}{
		"status": status,
	})
}

func BatchUpdateUploadQueueItemsSheetID(tx *gorm.DB, itemIDs []string, sheetID uuid.UUID) error {
	return BatchUpdateUploadQueueItems(tx, itemIDs, map[string]interface{}{
		"sheet_id": sheetID,
	})
}

func BatchUpdateUploadQueueItems(tx *gorm.DB, itemIDs []string, updateMap map[string]interface{}) error {
	return tx.Model(&UploadQueueItem{}).Where("uuid IN ?", itemIDs).Updates(updateMap).Error
}

func UpdateUploadQueueItem(tx *gorm.DB, itemID string, updateMap map[string]interface{}) error {
	return tx.Model(&UploadQueueItem{}).Where("uuid = ?", itemID).Updates(updateMap).Error
}

// FindUploadQueueItemsBySequence 根据队列ID和序列号查找上传队列项
func FindUploadQueueItemsBySequence(tx *gorm.DB, queueID string, sequence int) ([]*UploadQueueItem, error) {
	var items []*UploadQueueItem
	if err := tx.Where("queue_id = ? AND sequence = ?", queueID, sequence).Find(&items).Error; err != nil {
		return nil, err
	}
	return items, nil
}

// GetUploadQueueItemsBySheetID 根据试卷ID获取关联的上传队列项
func GetUploadQueueItemsBySheetID(tx *gorm.DB, sheetID string) ([]*UploadQueueItem, error) {
	var items []*UploadQueueItem

	// 通过SheetItem表关联查询UploadQueueItem
	query := tx.Table("upload_queue_items").
		Select("upload_queue_items.*").
		Joins("INNER JOIN sheet_items ON upload_queue_items.uuid = sheet_items.queue_item_id").
		Where("sheet_items.sheet_id = ?", sheetID).
		Order("upload_queue_items.created_at ASC")

	if err := query.Find(&items).Error; err != nil {
		return nil, err
	}

	return items, nil
}

// GetCompletedUploadQueueItemsByTaskID 获取任务下所有已完成批改的队列项
func GetCompletedUploadQueueItemsByTaskID(tx *gorm.DB, taskID uuid.UUID) ([]*UploadQueueItem, error) {
	var items []*UploadQueueItem

	query := tx.Table("upload_queue_items").
		Joins("LEFT JOIN upload_queues ON upload_queue_items.queue_id = upload_queues.uuid").
		Where("upload_queues.task_id = ?", taskID).
		Where("upload_queue_items.status = ?", CorrectionSheetStatusCompleted).
		Order("upload_queue_items.created_at ASC")

	if err := query.Find(&items).Error; err != nil {
		return nil, err
	}

	return items, nil
}

// UpdateUploadQueueItemMarkedFileID 更新队列项的标记文件ID
func UpdateUploadQueueItemMarkedFileID(tx *gorm.DB, itemID uuid.UUID, markedFileID string) error {
	return tx.Model(&UploadQueueItem{}).Where("uuid = ?", itemID).Update("marked_file_id", markedFileID).Error
}

// GetMarkedUploadQueueItemsByTaskID 获取任务下所有队列项（包括未标记的）
func GetMarkedUploadQueueItemsByTaskID(tx *gorm.DB, taskID uuid.UUID) ([]*UploadQueueItem, error) {
	var items []*UploadQueueItem

	query := tx.Table("upload_queue_items").
		Joins("LEFT JOIN upload_queues ON upload_queue_items.queue_id = upload_queues.uuid").
		Where("upload_queues.task_id = ?", taskID).
		Order("upload_queues.created_at ASC").
		Order("ABS(upload_queue_items.sequence) ASC").
		Order("upload_queue_items.sequence DESC")

	if err := query.Find(&items).Error; err != nil {
		return nil, err
	}

	return items, nil
}

func GetQueueDistanceOfFirstItem(tx *gorm.DB, currentItem *UploadQueueItem) (int64, error) {
	var cnt int64
	query := tx.Model(&UploadQueueItem{}).
		Where("queue_id = ?", currentItem.QueueID)

	// 根据正负数使用不同的比较条件
	absSeq := utils.IntAbs(currentItem.Sequence)
	if currentItem.Sequence < 0 {
		query = query.Where("ABS(sequence) <= ?", absSeq)
	} else {
		query = query.Where("ABS(sequence) < ?", absSeq)
	}

	// 使用Count方法更符合GORM习惯
	if err := query.Count(&cnt).Error; err != nil {
		return 0, err
	}

	// 正数需要加1（包含当前项）
	if currentItem.Sequence >= 0 {
		cnt++
	}

	return cnt, nil
}

func GetOrderedNthItems(tx *gorm.DB, queueID string, offset, limit int) ([]*UploadQueueItem, error) {
	var items []*UploadQueueItem
	// 先按seq绝对值，再按正负排序
	if err := tx.Where("queue_id = ?", queueID).Order("ABS(sequence) ASC").Order("sequence DESC").Limit(limit).Offset(offset).Find(&items).Error; err != nil {
		return nil, err
	}
	return items, nil
}
