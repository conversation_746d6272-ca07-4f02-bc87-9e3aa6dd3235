package model

import (
	"time"

	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
)

const (
	PrinterAuthStatusInit    = ""
	PrinterAuthStatusScanned = "scanned" // 已扫描
	PrinterAuthStatusBind    = "bind"    // 已绑定
)

// PrinterAuth 打印机授权信息
type PrinterAuth struct {
	gorm.Model

	// 基础信息
	UUID      uuid.UUID  `gorm:"uniqueIndex;type:uuid;default:uuid_generate_v4()"`
	QRCodeID  string     `gorm:"uniqueIndex;type:varchar(64)"` // 二维码ID
	TaskID    uuid.UUID  `gorm:"index;type:uuid"`              // 关联的任务ID
	Token     string     `gorm:"type:varchar(64)"`             // 访问令牌
	ExpiredAt time.Time  `gorm:""`                             // 过期时间
	Status    string     // 扫描状态
	ScannedAt *time.Time `gorm:""` // 扫描时间
	BindAt    *time.Time `gorm:""` // 绑定项目时间
}

func (p *PrinterAuth) TableName() string {
	return "printer_auths"
}

// IsExpired 检查授权是否已过期
func (p *PrinterAuth) IsExpired() bool {
	return p.ExpiredAt.Before(time.Now())
}

// CreatePrinterAuth 创建打印机授权
func CreatePrinterAuth(tx *gorm.DB, auth *PrinterAuth) error {
	return tx.Create(auth).Error
}

func UpdateNotScannedPrinterAuth(tx *gorm.DB, id string, data any) error {
	return tx.Model(&PrinterAuth{}).Where("uuid = ? and status = ?", id, PrinterAuthStatusScanned).UpdateColumns(data).Error
}

func GetPrinterAuthByID(tx *gorm.DB, id uuid.UUID) (*PrinterAuth, error) {
	var auth PrinterAuth
	if err := tx.Where("uuid = ?", id).First(&auth).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &auth, nil
}

// GetPrinterAuthByQRCodeID 通过二维码ID获取打印机授权
func GetPrinterAuthByQRCodeID(tx *gorm.DB, qrCodeID string) (*PrinterAuth, error) {
	var auth PrinterAuth
	if err := tx.Where("qr_code_id = ?", qrCodeID).First(&auth).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &auth, nil
}

// GetPrinterAuthByToken 通过Token获取打印机授权
func GetPrinterAuthByToken(tx *gorm.DB, token string) (*PrinterAuth, error) {
	var auth PrinterAuth
	if err := tx.Where("token = ?", token).First(&auth).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &auth, nil
}

// BindTaskToPrinterAuth 为二维码绑定任务
func BindTaskToPrinterAuth(tx *gorm.DB, qrCodeID string, taskID uuid.UUID, token string, expireTime time.Duration) (*PrinterAuth, error) {
	auth, err := GetPrinterAuthByQRCodeID(tx, qrCodeID)
	if err != nil {
		return nil, err
	}

	now := time.Now()
	expiredAt := now.Add(expireTime)

	// 如果不存在，则创建新的
	if auth == nil {
		auth = &PrinterAuth{
			UUID:      uuid.NewV4(),
			QRCodeID:  qrCodeID,
			TaskID:    taskID,
			Token:     token,
			ExpiredAt: expiredAt,
			ScannedAt: &now,
		}
		if err := CreatePrinterAuth(tx, auth); err != nil {
			return nil, err
		}
		return auth, nil
	}

	// 更新已存在的
	if err := tx.Model(&PrinterAuth{}).Where("qr_code_id = ?", qrCodeID).Updates(map[string]interface{}{
		"task_id":    taskID,
		"token":      token,
		"expired_at": expiredAt,
		"scanned_at": now,
	}).Error; err != nil {
		return nil, err
	}

	auth.TaskID = taskID
	auth.Token = token
	auth.ExpiredAt = expiredAt
	auth.ScannedAt = &now

	return auth, nil
}

// ListPrinterAuthsByTaskID 获取任务绑定的打印机列表
func ListPrinterAuthsByTaskID(tx *gorm.DB, taskID string) ([]*PrinterAuth, error) {
	var auths []*PrinterAuth
	if err := tx.Where("task_id = ? AND expired_at > ?", taskID, time.Now()).Find(&auths).Error; err != nil {
		return nil, err
	}
	return auths, nil
}

// UnbindPrinter 解绑打印机
func UnbindPrinter(tx *gorm.DB, bindID uuid.UUID) error {
	result := tx.Model(&PrinterAuth{}).
		Where("uuid = ?", bindID).Delete(&PrinterAuth{})
	if result.Error != nil {
		return result.Error
	}
	if result.RowsAffected == 0 {
		return gorm.ErrRecordNotFound
	}
	return nil
}

// GenerateQRCode 生成新的二维码
func GenerateQRCode(tx *gorm.DB, expireTime time.Duration) (*PrinterAuth, error) {
	qrCodeID := uuid.NewV4().String()
	now := time.Now()
	expiredAt := now.Add(expireTime)

	auth := &PrinterAuth{
		UUID:      uuid.NewV4(),
		QRCodeID:  qrCodeID,
		ExpiredAt: expiredAt,
	}

	if err := CreatePrinterAuth(tx, auth); err != nil {
		return nil, err
	}

	return auth, nil
}

func DeletePrinterAuth(tx *gorm.DB, id string) error {
	return tx.Delete(&PrinterAuth{}, "uuid = ?", id).Error
}
