package model

import (
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
)

type StudentAnswer struct {
	gorm.Model

	StudentID     uuid.UUID `gorm:"type:uuid;index"` // 学生ID
	ExaminationID uuid.UUID `gorm:"type:uuid;index"` // 试卷ID
	QuestionID    uuid.UUID `gorm:"type:uuid;index"` // 题目ID

	Answer      string
	AnswerImage string
	Correct     bool
}

func (sa *StudentAnswer) TableName() string {
	return "student_answers"
}

// CreateStudentAnswer creates a new student answer in the database
func CreateStudentAnswer(db *gorm.DB, studentAnswer *StudentAnswer) error {
	return db.Create(studentAnswer).Error
}

// GetStudentAnswerByID retrieves a student answer by its ID
func GetStudentAnswerByID(db *gorm.DB, id uint) (*StudentAnswer, error) {
	var studentAnswer StudentAnswer
	err := db.First(&studentAnswer, id).Error
	return &studentAnswer, err
}

// GetStudentAnswersByExamination retrieves all answers from a student for a specific examination
func GetStudentAnswersByExamination(db *gorm.DB, studentID, examinationID uuid.UUID) ([]StudentAnswer, error) {
	var answers []StudentAnswer
	err := db.Where("student_id = ? AND examination_id = ?", studentID, examinationID).
		Order("created_at").Find(&answers).Error
	return answers, err
}

// GetStudentAnswersByQuestion retrieves all student answers for a specific question
func GetStudentAnswersByQuestion(db *gorm.DB, questionID uuid.UUID) ([]StudentAnswer, error) {
	var answers []StudentAnswer
	err := db.Where("question_id = ?", questionID).Find(&answers).Error
	return answers, err
}

// GetAllStudentAnswers retrieves student answers with pagination and filters
func GetAllStudentAnswers(db *gorm.DB, filters map[string]interface{}, offset, limit int) ([]StudentAnswer, int64, error) {
	var answers []StudentAnswer
	var count int64

	query := db.Model(&StudentAnswer{})

	// Apply filters
	for key, value := range filters {
		query = query.Where(key+" = ?", value)
	}

	// Get total count
	if err := query.Count(&count).Error; err != nil {
		return nil, 0, err
	}

	// Apply pagination
	if limit > 0 {
		query = query.Limit(limit)
	}
	if offset > 0 {
		query = query.Offset(offset)
	}

	// Execute query
	err := query.Order("created_at DESC").Find(&answers).Error
	if err != nil {
		return nil, 0, err
	}

	return answers, count, nil
}

// UpdateStudentAnswer updates an existing student answer
func UpdateStudentAnswer(db *gorm.DB, studentAnswer *StudentAnswer) error {
	return db.Save(studentAnswer).Error
}

// DeleteStudentAnswer deletes a student answer by its ID
func DeleteStudentAnswer(db *gorm.DB, id uint) error {
	return db.Delete(&StudentAnswer{}, id).Error
}

// DeleteStudentAnswerByKeys deletes a student answer by its composite key
func DeleteStudentAnswerByKeys(db *gorm.DB, studentID, examinationID, questionID uuid.UUID) error {
	return db.Where("student_id = ? AND examination_id = ? AND question_id = ?",
		studentID, examinationID, questionID).Delete(&StudentAnswer{}).Error
}

// DeleteAllStudentAnswersByExamination deletes all answers from a student for a specific examination
func DeleteAllStudentAnswersByExamination(db *gorm.DB, studentID, examinationID uuid.UUID) error {
	return db.Where("student_id = ? AND examination_id = ?", studentID, examinationID).
		Delete(&StudentAnswer{}).Error
}

// BulkCreateStudentAnswers creates multiple student answers in a single transaction
func BulkCreateStudentAnswers(db *gorm.DB, answers []StudentAnswer) error {
	return db.Create(&answers).Error
}
