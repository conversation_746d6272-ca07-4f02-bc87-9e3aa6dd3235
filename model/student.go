package model

import (
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
)

type Student struct {
	gorm.Model

	UUID     uuid.UUID `gorm:"uniqueIndex;type:uuid;default:uuid_generate_v4()"`
	TenantID uuid.UUID `gorm:"type:uuid;index"` // 租户ID
	Number   string
	Name     string
	Gender   int8
}

func (m *Student) TableName() string {
	return "student"
}

// GetOrInitStudentWithNumber retrieves a student by number or creates a new one
func GetOrInitStudentWithTenantIdAndNumber(tx *gorm.DB, tenantID uuid.UUID, number string, newStudent *Student) (*Student, error) {
	var student Student
	if err := tx.Where(Student{Number: number, TenantID: tenantID}).Attrs(newStudent).FirstOrCreate(&student).Error; err != nil {
		return nil, err
	}
	return &student, nil
}

// CreateStudent creates a new student in the database
func CreateStudent(db *gorm.DB, student *Student) error {
	return db.Create(student).Error
}

// GetStudentByID retrieves a student by its ID
func GetStudentByID(db *gorm.DB, id uint) (*Student, error) {
	var student Student
	err := db.First(&student, id).Error
	return &student, err
}

// GetStudentByUUID retrieves a student by its UUID
func GetStudentByUUID(db *gorm.DB, uuid string) (*Student, error) {
	var student Student
	err := db.Where("uuid = ?", uuid).First(&student).Error
	return &student, err
}

// GetAllStudents retrieves all students with optional pagination and tenant filtering
func GetAllStudents(db *gorm.DB, tenantID uuid.UUID, offset, limit int) ([]Student, int64, error) {
	var students []Student
	var count int64

	query := db.Model(&Student{}).Where("tenant_id = ?", tenantID)

	if err := query.Count(&count).Error; err != nil {
		return nil, 0, err
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	if offset > 0 {
		query = query.Offset(offset)
	}

	if err := query.Find(&students).Error; err != nil {
		return nil, 0, err
	}

	return students, count, nil
}

// UpdateStudent updates an existing student
func UpdateStudent(db *gorm.DB, student *Student) error {
	return db.Save(student).Error
}

// DeleteStudent deletes a student by its ID
func DeleteStudent(db *gorm.DB, id uint) error {
	return db.Delete(&Student{}, id).Error
}

// DeleteStudentByUUID deletes a student by its UUID
func DeleteStudentByUUID(db *gorm.DB, uuid string) error {
	return db.Where("uuid = ?", uuid).Delete(&Student{}).Error
}
