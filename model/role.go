package model

import (
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
)

type Role struct {
	gorm.Model

	UUID uuid.UUID `gorm:"uniqueIndex;type:uuid;default:uuid_generate_v4()"`
	Name string    `gorm:"type:varchar(20)"`
	Code string    `gorm:"type:varchar(20);uniqueIndex"` // 角色代码
}

func (r *Role) TableName() string {
	return "roles"
}

// CreateRole creates a new role in the database
func CreateRole(db *gorm.DB, role *Role) error {
	return db.Create(role).Error
}

// GetRoleByID retrieves a role by its ID
func GetRoleByID(db *gorm.DB, id uint) (*Role, error) {
	var role Role
	err := db.First(&role, id).Error
	return &role, err
}

// GetRoleByUUID retrieves a role by its UUID
func GetRoleByUUID(db *gorm.DB, uuid string) (*Role, error) {
	var role Role
	err := db.Where("uuid = ?", uuid).First(&role).Error
	return &role, err
}

// GetRoleByCode retrieves a role by its code
func GetRoleByCode(db *gorm.DB, code string) (*Role, error) {
	var role Role
	err := db.Where("code = ?", code).First(&role).Error
	return &role, err
}

// GetAllRoles retrieves all roles with optional pagination
func GetAllRoles(db *gorm.DB, offset, limit int) ([]Role, int64, error) {
	var roles []Role
	var count int64

	query := db.Model(&Role{})

	if err := query.Count(&count).Error; err != nil {
		return nil, 0, err
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	if offset > 0 {
		query = query.Offset(offset)
	}

	if err := query.Find(&roles).Error; err != nil {
		return nil, 0, err
	}

	return roles, count, nil
}

// UpdateRole updates an existing role
func UpdateRole(db *gorm.DB, role *Role) error {
	return db.Save(role).Error
}

// DeleteRole deletes a role by its ID
func DeleteRole(db *gorm.DB, id uint) error {
	return db.Delete(&Role{}, id).Error
}
