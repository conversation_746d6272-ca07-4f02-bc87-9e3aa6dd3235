package model

import (
	"time"

	jsoniter "github.com/json-iterator/go"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
)

// CostControlRecord represents a record of cost control information
type CostControlRecord struct {
	gorm.Model

	// Basic information
	UUID   uuid.UUID `gorm:"uniqueIndex;type:uuid;default:uuid_generate_v4()"`
	Type   string    `gorm:"type:varchar(50);index"`  // Type of the cost control record
	TaskID uuid.UUID `gorm:"index;type:uuid"`         // Associated task ID
	UserID uuid.UUID `gorm:"index;type:uuid"`         // User who performed the action
	Data   string    `gorm:"type:text"`               // JSON data of the record
	Extra  string    `gorm:"type:text"`               // Extra information as JSON

	// Timestamps for analysis
	RecordedAt time.Time `gorm:"index"` // When the record was created
}

// TableName returns the table name
func (c *CostControlRecord) TableName() string {
	return "cost_control_records"
}

// CreateCostControlRecord creates a new cost control record in the database
func CreateCostControlRecord(db *gorm.DB, record *CostControlRecord) error {
	return db.Create(record).Error
}

// GetCostControlRecordsByTaskID retrieves cost control records by task ID
func GetCostControlRecordsByTaskID(db *gorm.DB, taskID uuid.UUID) ([]CostControlRecord, error) {
	var records []CostControlRecord
	err := db.Where("task_id = ?", taskID).Find(&records).Error
	return records, err
}

// GetCostControlRecordsByUserID retrieves cost control records by user ID
func GetCostControlRecordsByUserID(db *gorm.DB, userID uuid.UUID) ([]CostControlRecord, error) {
	var records []CostControlRecord
	err := db.Where("user_id = ?", userID).Find(&records).Error
	return records, err
}

// GetCostControlRecordsByType retrieves cost control records by type
func GetCostControlRecordsByType(db *gorm.DB, recordType string) ([]CostControlRecord, error) {
	var records []CostControlRecord
	err := db.Where("type = ?", recordType).Find(&records).Error
	return records, err
}

// SetData sets the data field with JSON serialization
func (c *CostControlRecord) SetData(data interface{}) error {
	jsonData, err := jsoniter.MarshalToString(data)
	if err != nil {
		return err
	}
	c.Data = jsonData
	return nil
}

// SetExtra sets the extra field with JSON serialization
func (c *CostControlRecord) SetExtra(extra map[string]string) error {
	jsonExtra, err := jsoniter.MarshalToString(extra)
	if err != nil {
		return err
	}
	c.Extra = jsonExtra
	return nil
}
