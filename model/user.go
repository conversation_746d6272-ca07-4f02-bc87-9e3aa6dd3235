package model

import (
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
)

type User struct {
	gorm.Model

	// 基础信息
	UUID     uuid.UUID `gorm:"uniqueIndex;type:uuid;default:uuid_generate_v4()"`
	Phone    string    `gorm:"type:varchar(20);unique;not null"` // 手机号
	Password string    `gorm:"type:varchar(255)"`
	Nickname string    `gorm:"type:varchar(100)"`
	Avatar   string    `gorm:"type:varchar(255)"`

	WxMiniProgramOpenID string `gorm:"column:wx_mini_program_open_id"` // 微信小程序openID
	WxUnionID           string `gorm:"column:wx_union_id"`             // 微信unionID
	WxNickName          string `gorm:"column:wx_nickname"`             // 微信昵称
	WxAvatarURL         string `gorm:"column:wx_avatar_url"`           // 微信头像
	WxGender            int    `gorm:"column:wx_gender"`               // 微信性别
	TmpUser             bool   `gorm:"column:tmp_user"`                // 是否为临时用户
}

func (u *User) TableName() string {
	return "users"
}

func GetOrInitUserWithPhone(tx *gorm.DB, phone string, newUser *User) (*User, error) {
	var user User
	if err := tx.Where(User{Phone: phone}).Attrs(newUser).FirstOrCreate(&user).Error; err != nil {
		return nil, err
	}
	return &user, nil
}

func GetUser(tx *gorm.DB, id string) (*User, error) {
	var user User
	if err := tx.First(&user, "uuid = ?", id).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

func GetUserWithPhone(tx *gorm.DB, phone string) (*User, error) {
	var user User
	if err := tx.First(&user, "phone = ?", phone).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &user, nil
}

func GetOrInitUserWithWxMiniProgramOpenID(tx *gorm.DB, openID string, newUser *User) (*User, bool, error) {
	var user User
	// 优先获取存在手机号信息的用户
	ret := tx.Where(User{WxMiniProgramOpenID: openID}).Attrs(newUser).Order("phone desc").FirstOrCreate(&user)
	if ret.Error != nil {
		return nil, false, ret.Error
	}
	return &user, ret.RowsAffected > 0, nil
}

func CreateUser(tx *gorm.DB, user *User) error {
	return tx.Create(user).Error
}

func UpdateUser(tx *gorm.DB, user *User) error {
	return tx.Save(user).Error
}

func DeleteUser(tx *gorm.DB, uuid string) error {
	return tx.Delete(&User{}, "uuid = ?", uuid).Error
}

// GetAllUsers retrieves all users with optional pagination
func GetAllUsers(db *gorm.DB, offset, limit int) ([]User, int64, error) {
	var users []User
	var count int64

	query := db.Model(&User{})

	if err := query.Count(&count).Error; err != nil {
		return nil, 0, err
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	if offset > 0 {
		query = query.Offset(offset)
	}

	if err := query.Find(&users).Error; err != nil {
		return nil, 0, err
	}

	return users, count, nil
}
