package model

// ContentItem 表示一个大题的内容
type ContentItem struct {
	Number   string            `json:"number,omitempty"`    // 大题题号
	SubItems []*SubContentItem `json:"sub_items,omitempty"` // 小题列表
}

// SubContentItem 表示一个小题的内容
type SubContentItem struct {
	Number      string  `json:"number,omitempty"`      // 题号，没有小题时为空字符串
	Score       float32 `json:"score,omitempty"`       // 题目总分
	Content     string  `json:"content,omitempty"`     // 题干，只有试卷时需要包含
	Answer      string  `json:"answer,omitempty"`      // 参考答案或学生作答
	Explanation string  `json:"explanation,omitempty"` // 解析，只有参考答案时才有
}
