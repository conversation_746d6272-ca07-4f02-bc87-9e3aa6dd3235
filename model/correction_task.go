package model

import (
	"encoding/json"
	"strings"

	jsoniter "github.com/json-iterator/go"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
)

// 批改任务状态
const (
	CorrectionTaskStatusWaitingForSettings = "waiting_for_settings" // 待完成设置
	CorrectionTaskStatusWaitingForPaper    = "waiting_for_paper"    // 等待扫描试卷
	CorrectionTaskStatusWaitingForAnswer   = "waiting_for_answer"   // 等待扫描答案
	CorrectionTaskStatusScanning           = "scanning"             // 扫描答题卡中
	CorrectionTaskStatusCompleted          = "completed"            // 已完成全部已扫描内容的批改
)

// 分组状态
const (
	GroupingStatusNone       = ""            // 未分组
	GroupingStatusInProgress = "in_progress" // 分组中
	GroupingStatusCompleted  = "completed"   // 分组完成
)

// 答题卡批改状态
const (
	CorrectionSheetStatusWaiting    = "waiting"     // 待批改
	CorrectionSheetStatusInProgress = "in_progress" // 批改中
	CorrectionSheetStatusCompleted  = "completed"   // 已批改
	CorrectionSheetStatusFailed     = "failed"      // 批改失败
)

// 解析状态
const (
	ParseStatusWaiting    = "waiting"     // 待解析
	ParseStatusInProgress = "in_progress" // 解析中
	ParseStatusCompleted  = "completed"   // 已解析
	ParseStatusFailed     = "failed"      // 解析失败
)

type QuestionNumber struct {
	From int `json:"from"`
	To   int `json:"to"`
}

type LLMCorrectResult struct {
	StudentNumber      string                 `json:"student_number" jsonschema_description:"学生学号，没有时留空"`                           // 学号
	MultiStudentNumber bool                   `json:"multi_student_number" jsonschema_description:"是否有多个学号，如果有多个学号则为true，否则为false"` // 是否有多个学号
	Answer             []LLMCorrectResultItem `json:"answer" jsonschema_description:"作答结果数组"`
}

type CorrectResult struct {
	StudentNumber      string               `json:"student_number"`       // 学号
	MultiStudentNumber bool                 `json:"multi_student_number"` // 是否有多个学号
	Answer             []*CorrectResultItem `json:"answer" jsonschema_description:"作答结果数组"`
}

type LLMCorrectResultItem struct {
	MainQuestion string  `json:"main_question" jsonschema_description:"大题题号，例如1"`                                           // 大题题号
	SubQuestion  string  `json:"sub_question" jsonschema_description:"小题题号，例如(2)，如果没有小题则为空字符串"`                             // 小题题号
	Score        float32 `json:"score" jsonschema_description:"这道题学生的最终得分，未说明则固定填-1"`                                       // 单题得分
	Reason       string  `json:"reason" jsonschema_description:"按点给分的题目中，需要给出给分理由及标准答案(不含解析)"`                              // 给分原因，常用于主观题
	Right        float32 `json:"right" jsonschema_description:"0-1 的数，代表当前学生作答与答案的匹配程度，0表示答案完全不匹配，1表示完全匹配，部分点答对则用小数代表匹配程度"` // 0-1 的数，代表当前学生作答与答案的匹配程度
}

type CorrectResultItem struct {
	LLMCorrectResultItem
	TopLeft     *Location `json:"top_left,omitempty"`     // 答题区域左上角坐标
	BottomRight *Location `json:"bottom_right,omitempty"` // 答题区域右下角坐标
	ItemID      string    `json:"item_id"`                // 上传队列项ID
}

// Number 返回完整题号，兼容旧版本
func (item *CorrectResultItem) Number() string {
	if item.SubQuestion == "" {
		return item.MainQuestion
	}
	return item.MainQuestion + item.SubQuestion
}

type Location struct {
	X json.Number `json:"x"`
	Y json.Number `json:"y"`
}

// CorrectionTask 批改任务
type CorrectionTask struct {
	gorm.Model

	// 基础信息
	UUID                uuid.UUID `gorm:"uniqueIndex;type:uuid;default:uuid_generate_v4()"`
	UserID              uuid.UUID `gorm:"index;type:uuid"`   // 创建者ID
	Title               string    `gorm:"type:varchar(255)"` // 任务标题
	Status              string    `gorm:"type:varchar(50)"`  // 任务状态
	NeedScanPaper       bool      `gorm:"default:false"`     // 是否需要扫描试卷题目
	PaperWithAnswer     bool      `gorm:"default:false"`     // 答题卡和试卷题目是否在一起
	SheetsPerPaper      int       `gorm:"default:1"`         // 每张答题卡有多少张纸
	DoubleSided         bool      `gorm:"default:false"`     // 是否双面打印
	VerticalArrangement bool      `gorm:"default:false"`     // 是否竖向放入
	MarkedFileChanged   bool      `gorm:"default:true"`      // 留痕结果是否改变
	TaskMarkedFileID    string    `gorm:"type:varchar(255)"` // 整个任务留痕文件ID

	// 试卷信息
	PaperDoubleSided         bool   `gorm:"default:false"`                      // 试卷是否双面打印
	PaperVerticalArrangement bool   `gorm:"default:false"`                      // 试卷是否竖向放入
	PaperFileIDs             string `gorm:"type:text"`                          // 试卷文件ID列表，以逗号分隔
	PaperFileQuestions       string `gorm:"type:text"`                          // 每张试卷的题目ID列表，json格式
	PaperContent             string `gorm:"type:text"`                          // 试卷内容解析结果，json格式
	PaperParseStatus         string `gorm:"type:varchar(20);default:'waiting'"` // 试卷解析状态

	// 答案信息
	AnswerDoubleSided         bool   `gorm:"default:false"`                      // 答案是否双面打印
	AnswerVerticalArrangement bool   `gorm:"default:false"`                      // 答案是否竖向放入
	AnswerFileIDs             string `gorm:"type:text"`                          // 答案文件ID列表，以逗号分隔
	AnswerFileQuestions       string `gorm:"type:text"`                          // 每张答案的题目ID列表，json格式
	AnswerContent             string `gorm:"type:text"`                          // 答案内容解析结果，json格式
	AnswerParseStatus         string `gorm:"type:varchar(20);default:'waiting'"` // 答案解析状态
}

// 文件ID数组相关方法
func (t *CorrectionTask) GetPaperFileIDsArray() []string {
	if t.PaperFileIDs == "" {
		return []string{}
	}
	return strings.Split(t.PaperFileIDs, ",")
}

func (t *CorrectionTask) SetPaperFileIDsArray(fileIDs []string) {
	t.PaperFileIDs = strings.Join(fileIDs, ",")
}

func (t *CorrectionTask) GetPaperFileQuestionsArray() [][]QuestionNumber {
	if t.PaperFileQuestions == "" {
		return nil
	}
	var questions [][]QuestionNumber
	if err := jsoniter.UnmarshalFromString(t.PaperFileQuestions, &questions); err != nil {
		return nil
	}
	return questions
}

func (t *CorrectionTask) SetPaperFileQuestionsArray(questions [][]QuestionNumber) {
	json, err := jsoniter.MarshalToString(questions)
	if err != nil {
		return
	}
	t.PaperFileQuestions = json
}

func (t *CorrectionTask) GetAnswerFileIDsArray() []string {
	if t.AnswerFileIDs == "" {
		return []string{}
	}
	return strings.Split(t.AnswerFileIDs, ",")
}

func (t *CorrectionTask) SetAnswerFileIDsArray(fileIDs []string) {
	t.AnswerFileIDs = strings.Join(fileIDs, ",")
}

func (t *CorrectionTask) GetAnswerFileQuestionsArray() [][]QuestionNumber {
	if t.AnswerFileQuestions == "" {
		return nil
	}
	var questions [][]QuestionNumber
	if err := jsoniter.UnmarshalFromString(t.AnswerFileQuestions, &questions); err != nil {
		return nil
	}
	return questions
}

func (t *CorrectionTask) SetAnswerFileQuestionsArray(questions [][]QuestionNumber) {
	json, err := jsoniter.MarshalToString(questions)
	if err != nil {
		return
	}
	t.AnswerFileQuestions = json
}

func (t *CorrectionTask) GetPaperContent() []*ContentItem {
	if t.PaperContent == "" {
		return nil
	}
	var content []*ContentItem
	if err := jsoniter.UnmarshalFromString(t.PaperContent, &content); err != nil {
		return nil
	}
	return content
}

func (t *CorrectionTask) SetPaperContent(content []*ContentItem) {
	json, err := jsoniter.MarshalToString(content)
	if err != nil {
		return
	}
	t.PaperContent = json
}

func (t *CorrectionTask) GetAnswerContent() []*ContentItem {
	if t.AnswerContent == "" {
		return nil
	}
	var content []*ContentItem
	if err := jsoniter.UnmarshalFromString(t.AnswerContent, &content); err != nil {
		return nil
	}
	return content
}

func (t *CorrectionTask) SetAnswerContent(content []*ContentItem) {
	json, err := jsoniter.MarshalToString(content)
	if err != nil {
		return
	}
	t.AnswerContent = json
}

func (t *CorrectionTask) TableName() string {
	return "correction_tasks"
}

// CreateCorrectionTask 创建批改任务
func CreateCorrectionTask(tx *gorm.DB, task *CorrectionTask) error {
	return tx.Create(task).Error
}

// GetCorrectionTask 获取批改任务
func GetCorrectionTask(tx *gorm.DB, taskID string) (*CorrectionTask, error) {
	var task CorrectionTask
	if err := tx.First(&task, "uuid = ?", taskID).Error; err != nil {
		if err == gorm.ErrRecordNotFound {
			return nil, nil
		}
		return nil, err
	}
	return &task, nil
}

// ListCorrectionTasks 获取用户的批改任务列表
func ListCorrectionTasks(tx *gorm.DB, userID string, limit, offset int) ([]*CorrectionTask, error) {
	var tasks []*CorrectionTask
	if err := tx.Where("user_id = ?", userID).Order("created_at DESC").Limit(limit).Offset(offset).Find(&tasks).Error; err != nil {
		return nil, err
	}
	return tasks, nil
}

// UpdateCorrectionTask 更新批改任务
func UpdateCorrectionTask(tx *gorm.DB, taskID string, updateMap map[string]interface{}) error {
	return tx.Model(&CorrectionTask{}).Where("uuid = ?", taskID).Updates(updateMap).Error
}

// CountCorrectionTasks 统计用户的批改任务总数
func CountCorrectionTasks(tx *gorm.DB, userID string) (int64, error) {
	var count int64
	if err := tx.Model(&CorrectionTask{}).Where("user_id = ?", userID).Count(&count).Error; err != nil {
		return 0, err
	}
	return count, nil
}

// GetActualStatus 根据配置和当前状态获取实际状态
func (t *CorrectionTask) GetActualStatus() string {
	// 如果当前状态是waiting_for_settings，直接返回
	if t.Status == CorrectionTaskStatusWaitingForSettings {
		return t.Status
	}

	// 如果当前状态是waiting_for_paper
	if t.Status == CorrectionTaskStatusWaitingForPaper {
		// 如果不需要扫描试卷，跳过等待试卷状态
		if !t.NeedScanPaper {
			return CorrectionTaskStatusWaitingForAnswer
		}

		// 如果已经有试卷文件，应该进入下一个状态
		if t.PaperFileIDs != "" {
			return CorrectionTaskStatusWaitingForAnswer
		}
	}

	// 如果当前状态是waiting_for_answer
	if t.Status == CorrectionTaskStatusWaitingForAnswer {
		// 如果已经有答案文件，应该进入scanning状态
		if t.AnswerFileIDs != "" {
			return CorrectionTaskStatusScanning
		}
	}

	// 其他情况直接返回当前状态
	return t.Status
}
