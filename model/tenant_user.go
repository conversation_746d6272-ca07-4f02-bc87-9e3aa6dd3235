package model

import (
	"strings"

	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
)

// TenantUserStatus defines the status of a tenant-user relationship
type TenantUserStatus string

const (
	// TenantUserStatusPending indicates the user's relationship with the tenant is pending approval
	TenantUserStatusPending TenantUserStatus = "pending"
	// TenantUserStatusValid indicates the user's relationship with the tenant is active and valid
	TenantUserStatusValid TenantUserStatus = "valid"
)

type TenantUser struct {
	gorm.Model

	UUID     uuid.UUID        `gorm:"uniqueIndex;type:uuid;default:uuid_generate_v4()"`
	TenantID uuid.UUID        `gorm:"index;type:uuid"`                    // 关联的租户UUID
	UserID   uuid.UUID        `gorm:"index;type:uuid"`                    // 关联的用户UUID
	Status   TenantUserStatus `gorm:"type:varchar(20);default:'pending'"` // User status in the tenant
}

func (tu *TenantUser) TableName() string {
	return "tenant_users"
}

// Create inserts a new TenantUser record into the database
func CreateTenantUser(db *gorm.DB, tenantUser *TenantUser) error {
	return db.Create(tenantUser).Error
}

// GetByUUID retrieves a TenantUser by its UUID
func GetTenantUserByUUID(db *gorm.DB, uuid string) (*TenantUser, error) {
	var tenantUser TenantUser
	err := db.Where("uuid = ?", uuid).First(&tenantUser).Error
	if err != nil {
		return nil, err
	}
	return &tenantUser, nil
}

// GetByTenantID retrieves all TenantUsers for a specific tenant
func GetTenantUsersByTenantID(db *gorm.DB, tenantID string) ([]TenantUser, error) {
	var tenantUsers []TenantUser
	err := db.Where("tenant_id = ?", tenantID).Find(&tenantUsers).Error
	if err != nil {
		return nil, err
	}
	return tenantUsers, nil
}

// GetByUserID retrieves all TenantUsers for a specific user
func GetTenantUsersByUserID(db *gorm.DB, userID string) ([]TenantUser, error) {
	var tenantUsers []TenantUser
	err := db.Where("user_id = ?", userID).Find(&tenantUsers).Error
	if err != nil {
		return nil, err
	}
	return tenantUsers, nil
}

// GetByTenantAndUserID retrieves a TenantUser by both tenant and user IDs
func GetTenantUserByTenantAndUserID(db *gorm.DB, tenantID string, userID string) (*TenantUser, error) {
	var tenantUser TenantUser
	err := db.Where("tenant_id = ? AND user_id = ?", tenantID, userID).First(&tenantUser).Error
	if err != nil {
		return nil, err
	}
	return &tenantUser, nil
}

// Update updates an existing TenantUser record
func UpdateTenantUser(db *gorm.DB, tenantUser *TenantUser) error {
	return db.Save(tenantUser).Error
}

// Delete removes a TenantUser record from the database
func DeleteTenantUser(db *gorm.DB, tenantUser *TenantUser) error {
	return db.Delete(tenantUser).Error
}

type TenantUserDetail struct {
	UUID        uuid.UUID
	TenantID    uuid.UUID // 关联的租户UUID
	UserID      uuid.UUID // 关联的用户UUID
	Nickname    string
	Phone       string
	Avatar      string
	WxNickName  string
	WxAvatarURL string
	Status      TenantUserStatus
	RoleID      uuid.UUID
	RoleName    string
}

// TenantUserFilter defines filter criteria for tenant user queries
type TenantUserFilter struct {
	Status   *TenantUserStatus
	TenantID *string
}

// GetAllTenantUsers retrieves tenant users with pagination and filtering, returning detailed information
func GetTenantUsers(db *gorm.DB, offset, limit int, filter *TenantUserFilter) ([]TenantUserDetail, int64, error) {
	var tenantUserDetails []TenantUserDetail
	var count int64

	// Prepare where clauses and parameters for both count and data queries
	var whereClause string
	var params []interface{}

	if filter != nil {
		var conditions []string

		if filter.Status != nil && string(*filter.Status) != "" {
			conditions = append(conditions, "tu.status = ?")
			params = append(params, *filter.Status)
		}

		if filter.TenantID != nil && string(*filter.TenantID) != "" {
			conditions = append(conditions, "tu.tenant_id = ?")
			params = append(params, *filter.TenantID)
		}

		if len(conditions) > 0 {
			whereClause = " WHERE " + strings.Join(conditions, " AND ")
		}
	}

	// Count total records using raw SQL
	countSQL := "SELECT COUNT(*) FROM tenant_users tu" +
		" LEFT JOIN users u ON tu.user_id = u.uuid" +
		" LEFT JOIN tenant_user_roles tur ON tu.uuid = tur.tenant_user_id" +
		" LEFT JOIN roles r ON tur.role_id = r.uuid" + whereClause

	if err := db.Raw(countSQL, params...).Scan(&count).Error; err != nil {
		return nil, 0, err
	}

	// Build the raw SQL query with joins and filters
	sql := `
        SELECT 
            tu.uuid, 
            tu.tenant_id, 
            tu.user_id, 
            tu.status,
            u.nickname, 
            u.phone, 
            u.avatar, 
            u.wx_nickname, 
            u.wx_avatar_url,
            r.uuid as role_id, 
            r.name as role_name
        FROM tenant_users tu
        LEFT JOIN users u ON tu.user_id = u.uuid
        LEFT JOIN tenant_user_roles tur ON tu.uuid = tur.tenant_user_id
        LEFT JOIN roles r ON tur.role_id = r.uuid` +
		whereClause + `
        ORDER BY tu.created_at DESC
        LIMIT ? OFFSET ?
    `

	// Add pagination parameters
	queryParams := append(params, limit, offset)

	// Execute the query with all parameters
	if err := db.Raw(sql, queryParams...).Scan(&tenantUserDetails).Error; err != nil {
		return nil, 0, err
	}

	return tenantUserDetails, count, nil
}
