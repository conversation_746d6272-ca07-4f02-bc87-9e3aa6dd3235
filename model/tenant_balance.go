package model

import (
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
)

type TenantBalance struct {
	gorm.Model

	UUID     uuid.UUID `gorm:"uniqueIndex;type:uuid;default:uuid_generate_v4()"`
	TenantID uuid.UUID `gorm:"index;type:uuid"` // 关联的租户UUID
	Point    int64     `gorm:"type:bigint"`     //点数
}

func (tb *TenantBalance) TableName() string {
	return "tenant_balances"
}

// CreateTenantBalance creates a new tenant balance record in the database
func CreateTenantBalance(db *gorm.DB, tb *TenantBalance) error {
	return db.Create(tb).Error
}

// GetTenantBalanceByUUID retrieves a tenant balance by its UUID
func GetTenantBalanceByUUID(db *gorm.DB, uuid uuid.UUID) (*TenantBalance, error) {
	var tb TenantBalance
	err := db.Where("uuid = ?", uuid).First(&tb).Error
	return &tb, err
}

// UpdateTenantBalance updates an existing tenant balance record
func UpdateTenantBalance(db *gorm.DB, tb *TenantBalance) error {
	return db.Save(tb).Error
}

// DeleteTenantBalance removes a tenant balance record from the database
func DeleteTenantBalance(db *gorm.DB, uuid uuid.UUID) error {
	return db.Where("uuid = ?", uuid).Delete(&TenantBalance{}).Error
}
