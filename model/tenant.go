package model

import (
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
)

type Tenant struct {
	gorm.Model

	UUID uuid.UUID `gorm:"uniqueIndex;type:uuid;default:uuid_generate_v4()"`
	Name string    `gorm:"type:varchar(20)"`
	Code string    `gorm:"type:varchar(20);uniqueIndex"` // 租户代码
}

func (t *Tenant) TableName() string {
	return "tenants"
}

// CreateTenant creates a new tenant in the database
func CreateTenant(db *gorm.DB, tenant *Tenant) error {
	return db.Create(tenant).Error
}

// GetTenantByUUID retrieves a tenant by its UUID
func GetTenantByUUID(db *gorm.DB, uuid string) (*Tenant, error) {
	var tenant Tenant
	err := db.Where("uuid = ?", uuid).First(&tenant).Error
	if err != nil {
		return nil, err
	}
	return &tenant, nil
}

// GetTenantByCode retrieves a tenant by its code
func GetTenantByCode(db *gorm.DB, code string) (*Tenant, error) {
	var tenant Tenant
	err := db.Where("code = ?", code).First(&tenant).Error
	if err != nil {
		return nil, err
	}
	return &tenant, nil
}

// GetTenants retrieves all tenants with optional pagination
func GetTenants(db *gorm.DB, offset, limit int) ([]Tenant, int64, error) {
	var tenants []Tenant
	var count int64

	query := db.Model(&Tenant{})

	if err := query.Count(&count).Error; err != nil {
		return nil, 0, err
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	if offset > 0 {
		query = query.Offset(offset)
	}

	if err := query.Find(&tenants).Error; err != nil {
		return nil, 0, err
	}

	return tenants, count, nil
}

// UpdateTenant updates an existing tenant in the database
func UpdateTenant(db *gorm.DB, tenant *Tenant) error {
	return db.Save(tenant).Error
}

// DeleteTenant deletes a tenant from the database
func DeleteTenant(db *gorm.DB, id uint) error {
	return db.Delete(&Tenant{}, id).Error
}

type TenantDetail struct {
	TenantID    string `gorm:"tenant_id"`
	TenantName  string `gorm:"tenant_name"`
	TenantPoint int64  `gorm:"tenant_point"`
}

func GetTenantDetailByID(db *gorm.DB, id string) (*TenantDetail, error) {
	var tenant TenantDetail

	query := `
		SELECT
			t.uuid AS tenant_id,
			t.name AS tenant_name,
			tb.point AS tenant_point
		FROM tenants t
		LEFT JOIN tenant_balances tb ON t.uuid = tb.tenant_id
		WHERE t.uuid = ?
	`

	err := db.Raw(query, id).First(&tenant).Error
	if err != nil {
		return nil, err
	}

	return &tenant, nil
}
