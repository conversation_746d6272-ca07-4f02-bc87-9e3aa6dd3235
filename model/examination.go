package model

import (
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
)

type Examination struct {
	gorm.Model

	UUID     uuid.UUID `gorm:"uniqueIndex;type:uuid;default:uuid_generate_v4()"`
	TenantID uuid.UUID `gorm:"type:uuid;index"` // 租户ID

	Grade   string `gorm:"type:varchar(64);index"` // 年级
	Subject string `gorm:"type:varchar(64);index"` // 科目
	Term    string `gorm:"type:varchar(64);index"` // 学期
	Title   string `gorm:"type:varchar(64);index"` // 试卷标题
}

func (m *Examination) TableName() string {
	return "examination"
}

// CreateExamination creates a new examination
func CreateExamination(db *gorm.DB, exam *Examination) error {
	return db.Create(exam).Error
}

// GetExaminationByID retrieves an examination by its ID
func GetExaminationByID(db *gorm.DB, id uint) (*Examination, error) {
	var exam Examination
	err := db.First(&exam, id).Error
	return &exam, err
}

// GetExaminationByUUID retrieves an examination by its UUID
func GetExaminationByUUID(db *gorm.DB, uuid string) (*Examination, error) {
	var exam Examination
	err := db.Where("uuid = ?", uuid).First(&exam).Error
	return &exam, err
}

// GetAllExaminations retrieves all examinations with optional pagination and filters
func GetAllExaminations(db *gorm.DB, tenantID uuid.UUID, filters map[string]string, offset, limit int) ([]Examination, int64, error) {
	var exams []Examination
	var count int64

	query := db.Model(&Examination{}).Where("tenant_id = ?", tenantID)

	// Apply filters if provided
	if grade, ok := filters["grade"]; ok && grade != "" {
		query = query.Where("grade = ?", grade)
	}
	if subject, ok := filters["subject"]; ok && subject != "" {
		query = query.Where("subject = ?", subject)
	}
	if term, ok := filters["term"]; ok && term != "" {
		query = query.Where("term = ?", term)
	}
	if title, ok := filters["title"]; ok && title != "" {
		query = query.Where("title LIKE ?", "%"+title+"%")
	}

	if err := query.Count(&count).Error; err != nil {
		return nil, 0, err
	}

	if limit > 0 {
		query = query.Limit(limit)
	}

	if offset > 0 {
		query = query.Offset(offset)
	}

	// Default sorting by creation date, newest first
	query = query.Order("created_at DESC")

	if err := query.Find(&exams).Error; err != nil {
		return nil, 0, err
	}

	return exams, count, nil
}

// UpdateExamination updates an existing examination
func UpdateExamination(db *gorm.DB, exam *Examination) error {
	return db.Save(exam).Error
}

// DeleteExamination deletes an examination by its ID
func DeleteExamination(db *gorm.DB, id uint) error {
	return db.Delete(&Examination{}, id).Error
}

// DeleteExaminationByUUID deletes an examination by its UUID
func DeleteExaminationByUUID(db *gorm.DB, uuid string) error {
	return db.Where("uuid = ?", uuid).Delete(&Examination{}).Error
}
