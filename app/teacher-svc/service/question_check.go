package service

import (
	"context"
	"fmt"

	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/teacher-svc/constant"
	appModel "codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/teacher-svc/model"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/model"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/utils"
	"codeup.aliyun.com/level-up/public/common/bizerr"
	"codeup.aliyun.com/level-up/public/common/logger"
	jsoniter "github.com/json-iterator/go"
	"github.com/openai/openai-go"
	"github.com/openai/openai-go/shared"
	"github.com/spf13/viper"
)

var exampleQuestionNumbers = []model.QuestionNumber{
	{
		From: 1,
		To:   10,
	},
}

func GetQuestionNumbersFromImage(ctx context.Context, fileURL string, preFileURLs []string) (any, []model.QuestionNumber, error) {
	logs := logger.GetLogger(ctx)

	client := GetOrInitBailianClient()
	exampleOutput, _ := jsoniter.MarshalToString(exampleQuestionNumbers)

	// 构建系统提示
	systemPrompt := fmt.Sprintf(`# 角色
你是一个专业的全科批改助手，专注于精准识别传入的答题卡、试卷、参考答案当前页内大题目的题号范围，前面几页作为参考以防止结果跨页导致无法正确识别大题。需明确，参考用图片的题号不纳入最终题号范围，仅考虑要识别图片的题号范围。

## 技能
### 技能 1: 识别题目范围
1. 当接收到答题卡、试卷、参考答案的图像时，仔细分析当前页图像内容，前面几页图像作为辅助参考。
2. 忽略小题题号（如 15.1 这种形式），精准识别当前页大题题号。
3. 若题号出现断开情况，例如当前页题号为 1、2、5、6、7、9，将其整理输出为三个数组项形式，每个数组项为闭区间，如 1-2，5-7，9-9。

## 限制:
- 只专注于识别和反馈当前页大题题号范围相关内容，拒绝回答无关话题。
- 所输出的内容必须按照要求整理成特定格式的数组项，不能偏离要求。
- 供参考的图片，一定不纳入识别范围
- %s一个参考的 json 格式: %s`, constant.OutputJSONPrompt, exampleOutput)

	// 构建消息
	messages := []openai.ChatCompletionMessageParamUnion{
		openai.SystemMessage(systemPrompt),
	}

	// 添加要识别的图片
	messages = append(messages, openai.UserMessage([]openai.ChatCompletionContentPartUnionParam{
		{OfText: &openai.ChatCompletionContentPartTextParam{
			Text: "# 输入\n## 要识别的图片\n",
		}},
		{OfImageURL: &openai.ChatCompletionContentPartImageParam{
			ImageURL: openai.ChatCompletionContentPartImageImageURLParam{
				URL:    fileURL,
				Detail: "high",
			},
		}},
	}))

	// 添加参考图片（如果有）
	for idx, preFileURL := range preFileURLs {
		messages = append(messages, openai.UserMessage([]openai.ChatCompletionContentPartUnionParam{
			{OfText: &openai.ChatCompletionContentPartTextParam{
				Text: "## 可供参考的前面几页图片(不纳入识别范围)\n",
			}},
		}))

		messages = append(messages, openai.UserMessage([]openai.ChatCompletionContentPartUnionParam{
			{OfText: &openai.ChatCompletionContentPartTextParam{
				Text: fmt.Sprintf("第%d张图片链接是：", idx+1),
			}},
			{OfImageURL: &openai.ChatCompletionContentPartImageParam{
				ImageURL: openai.ChatCompletionContentPartImageImageURLParam{
					URL:    preFileURL,
					Detail: "high",
				},
			}},
		}))
	}

	// 添加输出提示
	messages = append(messages, openai.UserMessage("# 输出\n\n你的输出是："))

	// 调用API
	modelName := viper.GetString("aliyun.bailian.model.visual-7b")
	chatCompletion, err := client.Chat.Completions.New(ctx, openai.ChatCompletionNewParams{
		Messages: messages,
		Model:    modelName,
		ResponseFormat: openai.ChatCompletionNewParamsResponseFormatUnion{
			OfText: &shared.ResponseFormatTextParam{
				Type: "json_object",
			},
		},
	})
	if err != nil {
		logs.WithError(err).Errorf("fail to create chat completion")
		return nil, nil, bizerr.WrapErr(constant.ErrCoze, err)
	}
	logs.Infof("result: %+v", utils.ToJson(chatCompletion))

	// 记录大模型消耗
	if chatCompletion.Usage.TotalTokens > 0 {
		usageData := appModel.LLMUsageData{
			Model:        modelName,
			InputTokens:  chatCompletion.Usage.PromptTokens,
			OutputTokens: chatCompletion.Usage.CompletionTokens,
			TotalTokens:  chatCompletion.Usage.TotalTokens,
			Context:      "Question number recognition",
			RawResponse:  utils.ToJson(chatCompletion),
		}

		// 记录额外信息
		extra := map[string]string{
			"reference_image_count": fmt.Sprintf("%d", len(preFileURLs)),
		}

		// 调用记录函数
		costControlInfo := &model.CostControlInfo{
			RecordType: constant.RecordTypeLLMQuestionNumberRecognition,
			TaskID:     "",
			UserID:     "",
		}
		if err := RecordCostControlInfo(ctx, costControlInfo, usageData, extra); err != nil {
			logs.WithError(err).Warn("Failed to record LLM usage data")
		}
	}

	questionNumbers := make([]model.QuestionNumber, 0)
	if err := utils.JSON.UnmarshalFromString(chatCompletion.Choices[0].Message.Content, &questionNumbers); err != nil {
		logs.WithError(err).Errorf("fail to unmarshal question numbers, resp: %+v", chatCompletion)
		return &chatCompletion, nil, bizerr.WrapErr(constant.ErrCoze, err)
	}

	return &chatCompletion, questionNumbers, nil
}
