package service

import (
	"context"
	"sort"

	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/management-svc/constant"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/model"
	"codeup.aliyun.com/level-up/public/common/bizerr"
	"codeup.aliyun.com/level-up/public/common/logger"
	"codeup.aliyun.com/level-up/public/common/utils"
	mapset "github.com/deckarep/golang-set/v2"
	"gorm.io/gorm"
)

// CombineItemToSheet 基于给定的itemID，如果是最后一页，那么组装出sheet并返回，否则返回nil
func CombineItemToSheet(ctx context.Context, db *gorm.DB, itemID string) (*model.Sheet, error) {
	logs := logger.GetLogger(ctx)
	// 获取队列和批改任务，知道一份答题卡有多少页
	items, err := model.GetUploadQueueItemsByIDs(db, []string{itemID})
	if err != nil {
		logs.WithError(err).Errorf("fail to GetUploadQueueItemsByIDs of %s", itemID)
		return nil, bizerr.WrapErr(constant.ErrDB, err)
	}
	if len(items) == 0 {
		logs.Warnf("item %s not found", itemID)
		return nil, nil
	}
	item := items[0]
	queue, err := model.GetUploadQueue(db, item.QueueID.String())
	if err != nil {
		logs.WithError(err).Errorf("fail to get queue %s", item.QueueID.String())
		return nil, bizerr.WrapErr(constant.ErrDB, err)
	}
	if queue == nil {
		logs.Warnf("queue %s not found", item.QueueID.String())
		return nil, nil
	}
	task, err := model.GetCorrectionTask(db, queue.TaskID.String())
	if err != nil {
		logs.WithError(err).Errorf("fail to get task %s", queue.TaskID.String())
		return nil, bizerr.WrapErr(constant.ErrDB, err)
	}
	if task == nil {
		logs.Warnf("task %s not found", queue.TaskID.String())
		return nil, nil
	}
	// 找到同个队列中，距离第一页的张数
	distance, err := model.GetQueueDistanceOfFirstItem(db, item)
	if err != nil {
		logs.WithError(err).Errorf("fail to get queue distance of first item %s", itemID)
		return nil, bizerr.WrapErr(constant.ErrDB, err)
	}
	logs.Infof("distance from item %s to first item is %d", itemID, distance)
	// 如果不是整数倍，直接返回nil
	if distance%int64(task.SheetsPerPaper) != 0 {
		logs.Info("not a full sheet, skip")
		return nil, nil
	}
	// 如果是答题卡的整数倍，则找到该学生的全部答题卡
	lastItemOrder := distance - int64(task.SheetsPerPaper)
	currentItems, err := model.GetOrderedNthItems(db, queue.UUID.String(), int(lastItemOrder), int(task.SheetsPerPaper))
	if err != nil {
		logs.WithError(err).Errorf("fail to get ordered nth items, lastItemOrder: %d, sheetsPerPaper: %d", lastItemOrder, task.SheetsPerPaper)
		return nil, bizerr.WrapErr(constant.ErrDB, err)
	}
	// 如果是双面扫描，校验所有答题卡的sequence是否正确
	if task.DoubleSided {
		seqSet := mapset.NewSet[int]()
		for _, item := range currentItems {
			seqSet.Add(utils.IntAbs(item.Sequence))
		}
		if (task.SheetsPerPaper+1)/2 != seqSet.Cardinality() {
			logs.Errorf("double sided scan but sequence not match, expect %d, got %d", (task.SheetsPerPaper+1)/2, seqSet.Cardinality())
			return nil, bizerr.NewBizErrf(constant.ErrServer, "item sequence not match")
		}
	}
	// 生成Sheet和SheetItem并返回
	sheet := &model.Sheet{
		TaskID: task.UUID,
		Status: model.CorrectionSheetStatusInProgress,
	}
	if err := db.Transaction(func(tx *gorm.DB) error {
		if err := model.CreateSheet(tx, sheet); err != nil {
			return bizerr.WrapErr(constant.ErrDB, err)
		}

		var sheetItems []*model.SheetItem
		for _, item := range currentItems {
			sheetItem := &model.SheetItem{
				SheetID:     sheet.UUID,
				QueueItemID: item.UUID,
			}
			sheetItems = append(sheetItems, sheetItem)
		}
		if err := model.BatchCreate(tx, sheetItems, 100); err != nil {
			return bizerr.WrapErr(constant.ErrDB, err)
		}
		return nil
	}); err != nil {
		logs.WithError(err).Errorf("fail to create sheet for item %s", itemID)
		return nil, err
	}
	return sheet, nil
}

func SortSheetItems(ctx context.Context, items []*model.UploadQueueItem) {
	sort.Slice(items, func(i, j int) bool {
		iAbsSeq := utils.IntAbs(items[i].Sequence)
		jAbsSeq := utils.IntAbs(items[j].Sequence)
		if iAbsSeq != jAbsSeq {
			return iAbsSeq < jAbsSeq
		}
		return items[i].Sequence > items[j].Sequence
	})
}
