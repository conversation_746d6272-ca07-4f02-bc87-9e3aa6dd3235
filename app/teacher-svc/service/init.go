package service

import (
	"sync"

	"github.com/openai/openai-go"
	"github.com/openai/openai-go/option"
	"github.com/spf13/viper"
)

var (
	bailianClient   *openai.Client
	bailianInitOnce sync.Once

	volcengineClient   *openai.Client
	volcengineInitOnce sync.Once

	batchVolcengineClient   *openai.Client
	batchVolcengineInitOnce sync.Once
)

func GetOrInitBailianClient() *openai.Client {
	bailianInitOnce.Do(func() {
		cli := openai.NewClient(
			option.WithAPIKey(viper.GetString("aliyun.bailian.api_key")),
			option.WithBaseURL(viper.GetString("aliyun.bailian.base_url")),
		)
		bailianClient = &cli
	})
	return bailianClient
}

func GetOrInitVolcengineClient() *openai.Client {
	volcengineInitOnce.Do(func() {
		cli := openai.NewClient(
			option.WithAPIKey(viper.GetString("volcengine.ark.api_key")),
			option.WithBaseURL(viper.GetString("volcengine.ark.base_url")),
		)
		volcengineClient = &cli
	})
	return volcengineClient
}

func GetOrInitBatchVolcengineClient() *openai.Client {
	batchVolcengineInitOnce.Do(func() {
		cli := openai.NewClient(
			option.WithAPIKey(viper.GetString("volcengine.ark.api_key")),
			option.WithBaseURL(viper.GetString("volcengine.ark.batch_base_url")),
		)
		batchVolcengineClient = &cli
	})
	return batchVolcengineClient
}
