package service

import (
	"context"
	"time"

	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/teacher-svc/constant"
	"codeup.aliyun.com/level-up/public/common/bizerr"
	"codeup.aliyun.com/level-up/public/common/clients/aliyun"
	rclient "codeup.aliyun.com/level-up/public/common/clients/redis"
	"codeup.aliyun.com/level-up/public/common/logger"
	"codeup.aliyun.com/level-up/public/common/utils"
	smsclient "github.com/alibabacloud-go/dysmsapi-20170525/v3/client"
	"github.com/alibabacloud-go/tea/tea"
	"github.com/redis/go-redis/v9"
	"github.com/spf13/viper"
)

const (
	MaxUserTokenExpTime     = time.Hour * 24 * 7
	MaxCodeFailTimes        = 5 // 最大允许的code校验错误次数
	ValidationCodeLength    = 6
	CodeExpireTime          = time.Minute * 5
	ValidationCodeMaxPerDay = 50
	ValidationCodeRate      = time.Second * 60
)

func ValidatePhoneCode(ctx context.Context, phone, code string) (string, error) {
	logs := logger.GetLogger(ctx)
	// 校验code
	key := rclient.GetPhoneSMSCodeKey(phone)
	timesKey := rclient.GetPhoneSMSValidationTimesKey(phone)
	client := rclient.GetRedisClient()
	realCode, err := client.Get(ctx, key).Result()
	if err != nil {
		logs.WithError(err).Errorf("fail to get code with phone %s", phone)
		if err == redis.Nil {
			return "验证码已过期", bizerr.NewBizErrf(constant.ErrAuth, "code expired")
		}
		return "", bizerr.WrapErr(constant.ErrDB, err)
	}
	if code == "" || realCode != code {
		// 校验次数过多则直接删除该code
		times, err := client.Incr(ctx, timesKey).Result()
		if err != nil {
			logs.WithError(err).Errorf("fail to incr fail times of phone %s", phone)
		}
		// 失败次数过多则删除code
		if times >= MaxCodeFailTimes {
			if err := client.Del(ctx, key).Err(); err != nil {
				logs.WithError(err).Errorf("fail to del code of phone %s because too many retry times", phone)
				return "错误次数过多，请重新验证", bizerr.NewBizErrf(constant.ErrAuth, "please resend code")
			}
		}
		return "验证码错误", bizerr.NewBizErrf(constant.ErrAuth, "invalid code")
	}
	// 校验成功则删除code和次数
	pipe := client.Pipeline()
	if err := pipe.Del(ctx, key).Err(); err != nil {
		logs.WithError(err).Errorf("fail to delete code with phone %s", phone)
		return "", bizerr.WrapErr(constant.ErrDB, err)
	}
	if err := pipe.Del(ctx, timesKey).Err(); err != nil {
		logs.WithError(err).Errorf("fail to delete retry times with phone %s", phone)
		return "", bizerr.WrapErr(constant.ErrDB, err)
	}
	cmder, err := pipe.Exec(ctx)
	if err != nil {
		logs.WithError(err).Errorf("fail to exec del pipe with phone %s", phone)
		return "", bizerr.WrapErr(constant.ErrDB, err)
	}
	for _, cmd := range cmder {
		if cmd.Err() != nil {
			logs.WithError(err).Errorf("fail to exec pipe with cmd %s", cmd.String())
			return "", bizerr.WrapErr(constant.ErrDB, err)
		}
	}

	return "", nil
}

func SendValidaionCode(ctx context.Context, phone string) (string, error) {
	logs := logger.GetLogger(ctx)
	// 生成验证码
	code := utils.GenerateValidationCode(ValidationCodeLength)
	// 验证码写入数据库，注意这里要先写再发，发送失败重新生成就行了
	ok, err := CanSend(ctx, phone)
	if err != nil {
		return "", err
	}
	if !ok {
		return "发送太快了，或者当日发送次数过多", bizerr.NewBizErrf(constant.ErrRateLimit, "exceed send rate")
	}
	key := rclient.GetPhoneSMSCodeKey(phone)
	rateKey := rclient.GetPhoneSMSSendRateKey(phone)
	client := rclient.GetRedisClient()
	pipe := client.Pipeline()
	if err := pipe.SetEx(ctx, key, code, CodeExpireTime).Err(); err != nil {
		logs.WithError(err).Errorf("fail to pipe key %s", key)
		return "", bizerr.WrapErr(constant.ErrDB, err)
	}
	if err := pipe.SetEx(ctx, rateKey, 1, ValidationCodeRate).Err(); err != nil {
		logs.WithError(err).Errorf("fail to pipe key %s", rateKey)
		return "", bizerr.WrapErr(constant.ErrDB, err)
	}
	cmder, err := pipe.Exec(ctx)
	if err != nil {
		logs.WithError(err).Errorf("fail to exec pipe")
		return "", bizerr.WrapErr(constant.ErrDB, err)
	}
	for _, cmd := range cmder {
		if cmd.Err() != nil {
			logs.WithError(err).Errorf("fail to exec pipe with cmd %s", cmd.String())
			return "", bizerr.WrapErr(constant.ErrDB, err)
		}
	}
	// 实际发送验证码
	sign := viper.GetString("aliyun.sms.sign")
	tplCode := viper.GetString("aliyun.sms.template")
	res, err := aliyun.GetSMSClient().SendSms(&smsclient.SendSmsRequest{
		SignName:      &sign,
		TemplateCode:  tea.String(tplCode),
		PhoneNumbers:  tea.String(phone),
		TemplateParam: tea.String("{\"code\":\"" + code + "\"}"),
	})
	if err != nil {
		logs.WithError(err).Errorf("fail to send sms to %s", phone)
		return "", bizerr.WrapErr(constant.ErrSMS, err)
	}
	if res.Body.Code != nil && *res.Body.Code != "OK" {
		msg := ""
		if res.Body.Message != nil {
			msg = *res.Body.Message
		}
		logs.Errorf("fail to send sms to %s, code: %s, result: %+v", phone, *res.Body.Code, res.Body)
		return "", bizerr.NewBizErrf(constant.ErrSMS, msg)
	}

	return "", nil
}

func CanSend(ctx context.Context, phone string) (bool, error) {
	logs := logger.GetLogger(ctx)
	// 校验当天总数是否超限
	timesKey := rclient.GetPhoneSMSSendTimesKey(phone)
	client := rclient.GetRedisClient()
	pipe := client.TxPipeline()
	times, err := pipe.Incr(ctx, timesKey).Result()
	if err != nil {
		logs.WithError(err).Errorf("fail to get key %s", timesKey)
		return false, bizerr.WrapErr(constant.ErrDB, err)
	}
	if times == 1 {
		if err := pipe.Expire(ctx, timesKey, time.Hour*24).Err(); err != nil {
			logs.WithError(err).Errorf("fail to ttl key %s", timesKey)
			return false, bizerr.WrapErr(constant.ErrDB, err)
		}
	}
	pipe.Expire(ctx, timesKey, time.Hour*24)
	cmder, err := pipe.Exec(ctx)
	if err != nil {
		logs.WithError(err).Errorf("fail to exec pipe with key %s", timesKey)
		return false, bizerr.WrapErr(constant.ErrDB, err)
	}
	for _, cmd := range cmder {
		if cmd.Err() != nil {
			logs.WithError(err).Errorf("fail to exec pipe with args %s", cmd.String())
			return false, bizerr.WrapErr(constant.ErrDB, err)
		}
	}
	if times >= ValidationCodeMaxPerDay {
		return false, nil
	}
	// 校验频率是否超限
	rateKey := rclient.GetPhoneSMSSendRateKey(phone)
	rate, err := client.Exists(ctx, rateKey).Result()
	if err != nil {
		return false, bizerr.WrapErr(constant.ErrDB, err)
	}
	if rate > 0 {
		return false, nil
	}
	return true, nil
}
