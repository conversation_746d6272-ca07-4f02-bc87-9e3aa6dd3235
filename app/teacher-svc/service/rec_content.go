package service

import (
	"context"
	"fmt"

	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/teacher-svc/constant"
	appModel "codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/teacher-svc/model"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/model"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/utils"
	"codeup.aliyun.com/level-up/public/common/bizerr"
	"codeup.aliyun.com/level-up/public/common/logger"
	jsoniter "github.com/json-iterator/go"
	"github.com/openai/openai-go"
	"github.com/openai/openai-go/shared"
	"github.com/spf13/viper"
)

// 试卷内容示例输出结构
var examplePaperContentResult = []*model.ContentItem{
	{
		Number: "1",
		SubItems: []*model.SubContentItem{
			{
				Number:  "(1)",
				Score:   3,
				Content: "试卷题干内容",
			},
		},
	},
}

// 参考答案示例输出结构
var exampleAnswerContentResult = []*model.ContentItem{
	{
		Number: "1",
		SubItems: []*model.SubContentItem{
			{
				Number:      "(1)",
				Score:       3,
				Answer:      "参考答案内容",
				Explanation: "答案解析",
			},
		},
	},
}

// 答题卡示例输出结构
var exampleAnswerSheetContentResult = []*model.ContentItem{
	{
		Number: "1",
		SubItems: []*model.SubContentItem{
			{
				Number: "(1)",
				Score:  3,
				Answer: "学生作答内容",
			},
		},
	},
}

// RecognizePaperContent 识别试卷内容
func RecognizePaperContent(ctx context.Context, fileURLs []string) (any, []*model.ContentItem, error) {
	logs := logger.GetLogger(ctx)

	client := GetOrInitBailianClient()
	exampleOutput, _ := jsoniter.MarshalToString(examplePaperContentResult)

	// 构建系统提示
	systemPrompt := fmt.Sprintf(`# 角色
你是一个专业的试卷内容识别助手，专注于精准识别传入的试卷图片，并将其转换为结构化的内容。

## 技能
### 技能 1: 识别试卷内容
1. 当接收到试卷图像时，仔细分析图像内容，识别出所有大题和小题。
2. 对于每个大题，提取其中的所有小题，包括题号和题干内容。
3. 如果一个大题没有小题，也要创建一个子项，但题号为空字符串。

## 输出格式
输出为一个数组，每个数组项代表一个大题，每个大题包含：
- number: 大题题号，如"一"、"二"、"I"、"II"等
- sub_items: 小题数组，每个小题包含：
  - number: 小题题号，如果没有小题则为空字符串
  - score: 这道题的分值，如果参考答案上标注了分值则提取，否则填-1
  - content: 题干内容
  - answer: 空字符串（试卷中没有答案）
  - explanation: 空字符串（试卷中没有解析）

## 限制:
- 只专注于识别和反馈试卷内容，拒绝回答无关话题。
- 所输出的内容必须按照要求整理成特定格式的数组项，不能偏离要求。
- %s一个参考的 json 格式: %s`, constant.OutputJSONPrompt, exampleOutput)

	// 构建消息
	messages := []openai.ChatCompletionMessageParamUnion{
		openai.SystemMessage(systemPrompt),
	}

	// 添加所有图片
	for idx, fileURL := range fileURLs {
		imagePrompt := fmt.Sprintf("## 第%d张试卷图片\n", idx+1)
		messages = append(messages, openai.UserMessage([]openai.ChatCompletionContentPartUnionParam{
			{OfText: &openai.ChatCompletionContentPartTextParam{
				Text: imagePrompt,
			}},
			{OfImageURL: &openai.ChatCompletionContentPartImageParam{
				ImageURL: openai.ChatCompletionContentPartImageImageURLParam{
					URL:    fileURL,
					Detail: "high",
				},
			}},
		}))
	}

	// 添加输出提示
	messages = append(messages, openai.UserMessage("# 输出\n\n请输出识别结果："))

	// 调用API
	modelName := viper.GetString("aliyun.bailian.model.visual-7b")
	chatCompletion, err := client.Chat.Completions.New(ctx, openai.ChatCompletionNewParams{
		Messages: messages,
		Model:    modelName,
		ResponseFormat: openai.ChatCompletionNewParamsResponseFormatUnion{
			OfText: &shared.ResponseFormatTextParam{
				Type: "json_object",
			},
		},
	})
	if err != nil {
		logs.WithError(err).Errorf("fail to create chat completion")
		return nil, nil, bizerr.WrapErr(constant.ErrCoze, err)
	}
	logs.Infof("result: %+v", utils.ToJson(chatCompletion))

	// 记录大模型消耗
	if chatCompletion.Usage.TotalTokens > 0 {
		usageData := appModel.LLMUsageData{
			Model:        modelName,
			InputTokens:  chatCompletion.Usage.PromptTokens,
			OutputTokens: chatCompletion.Usage.CompletionTokens,
			TotalTokens:  chatCompletion.Usage.TotalTokens,
			Context:      fmt.Sprintf("Paper recognition with %d images", len(fileURLs)),
			RawResponse:  utils.ToJson(chatCompletion),
		}

		// 记录额外信息
		extra := map[string]string{
			"image_count": fmt.Sprintf("%d", len(fileURLs)),
		}

		// 调用记录函数
		costControlInfo := &model.CostControlInfo{
			RecordType: constant.RecordTypeLLMPaperRecognition,
			TaskID:     "",
			UserID:     "",
		}
		if err := RecordCostControlInfo(ctx, costControlInfo, usageData, extra); err != nil {
			logs.WithError(err).Warn("Failed to record LLM usage data")
		}
	}

	contentItems := make([]*model.ContentItem, 0)
	if err := utils.JSON.UnmarshalFromString(chatCompletion.Choices[0].Message.Content, &contentItems); err != nil {
		logs.WithError(err).Errorf("fail to unmarshal content items, resp: %+v", chatCompletion)
		return &chatCompletion, nil, bizerr.WrapErr(constant.ErrCoze, err)
	}

	return &chatCompletion, contentItems, nil
}

// RecognizeAnswerContent 识别参考答案内容
func RecognizeAnswerContent(ctx context.Context, fileURLs []string) (any, []*model.ContentItem, error) {
	logs := logger.GetLogger(ctx)

	client := GetOrInitBailianClient()
	exampleOutput, _ := jsoniter.MarshalToString(exampleAnswerContentResult)

	// 构建系统提示
	systemPrompt := fmt.Sprintf(`# 角色
你是一个专业的参考答案识别助手，专注于精准识别传入的参考答案图片，并将其转换为结构化的内容。

## 技能
### 技能 1: 识别参考答案内容
1. 当接收到参考答案图像时，仔细分析图像内容，识别出所有大题和小题。
2. 对于每个大题，提取其中的所有小题，包括题号、答案内容和解析。
3. 如果一个大题是单独的一道题(选择、填空题等)，没有小题，也要创建一个子项，但题号为空字符串。
4. 对于给出的选择题和填空题，每道题算作一个大题，填空题里面的多个空算作一个小题
5. 每一张图片里面的内容都需要识别，例如：有的图片会以"1-5 A B C A C"这种形式表示选择题，这类都需要在结果中包括
6. 一道独立的题目就是一道大题，例如一道选择题、填空题、问答题等。而大题下面的每个小问则对应一道小题
7. 输出的结果中，每道题对应一项，不要用例如“1-5”等方式在一项里面包含多道题，应该拆开输出

## 输出格式
输出为一个数组，每个数组项代表一个大题，每个大题包含：
- number: 大题题号，如"一"、"二"、"I"、"II"、"1"等
- sub_items: 小题数组，每个小题包含：
  - number: 小题题号，如"(1)"等，如果没有小题则为空字符串
  - score: 这道题的分值，如果参考答案上标注了分值则提取，否则填-1
  - content: 空字符串（参考答案中通常不包含题干）
  - answer: 参考答案内容
  - explanation: 答案解析内容

## 限制:
- 只专注于识别和反馈参考答案内容，拒绝回答无关话题。
- 所输出的内容必须按照要求整理成特定格式的数组项，不能偏离要求。
- %s一个参考的 json 格式: %s`, constant.OutputJSONPrompt, exampleOutput)

	// 构建消息
	messages := []openai.ChatCompletionMessageParamUnion{
		openai.SystemMessage(systemPrompt),
	}

	// 添加所有图片
	for idx, fileURL := range fileURLs {
		imagePrompt := fmt.Sprintf("## 第%d张参考答案图片\n", idx+1)
		messages = append(messages, openai.UserMessage([]openai.ChatCompletionContentPartUnionParam{
			{OfText: &openai.ChatCompletionContentPartTextParam{
				Text: imagePrompt,
			}},
			{OfImageURL: &openai.ChatCompletionContentPartImageParam{
				ImageURL: openai.ChatCompletionContentPartImageImageURLParam{
					URL:    fileURL,
					Detail: "high",
				},
			}},
		}))
	}

	// 添加输出提示
	messages = append(messages, openai.UserMessage("# 输出\n\n请输出识别结果："))

	// 调用API
	modelName := viper.GetString("aliyun.bailian.model.visual-72b")
	chatCompletion, err := client.Chat.Completions.New(ctx, openai.ChatCompletionNewParams{
		Messages: messages,
		Model:    modelName,
		ResponseFormat: openai.ChatCompletionNewParamsResponseFormatUnion{
			OfText: &shared.ResponseFormatTextParam{
				Type: "json_object",
			},
		},
	})
	if err != nil {
		logs.WithError(err).Errorf("fail to create chat completion")
		return nil, nil, bizerr.WrapErr(constant.ErrCoze, err)
	}
	logs.Infof("result: %+v", utils.ToJson(chatCompletion))

	// 记录大模型消耗
	if chatCompletion.Usage.TotalTokens > 0 {
		usageData := appModel.LLMUsageData{
			Model:        modelName,
			InputTokens:  chatCompletion.Usage.PromptTokens,
			OutputTokens: chatCompletion.Usage.CompletionTokens,
			TotalTokens:  chatCompletion.Usage.TotalTokens,
			Context:      fmt.Sprintf("Answer recognition with %d images", len(fileURLs)),
			RawResponse:  utils.ToJson(chatCompletion),
		}

		// 记录额外信息
		extra := map[string]string{
			"image_count": fmt.Sprintf("%d", len(fileURLs)),
		}

		// 调用记录函数
		costControlInfo := &model.CostControlInfo{
			RecordType: constant.RecordTypeLLMAnswerRecognition,
			TaskID:     "",
			UserID:     "",
		}
		if err := RecordCostControlInfo(ctx, costControlInfo, usageData, extra); err != nil {
			logs.WithError(err).Warn("Failed to record LLM usage data")
		}
	}

	contentItems := make([]*model.ContentItem, 0)
	if err := utils.JSON.UnmarshalFromString(chatCompletion.Choices[0].Message.Content, &contentItems); err != nil {
		logs.WithError(err).Errorf("fail to unmarshal content items, resp: %+v", chatCompletion)
		return &chatCompletion, nil, bizerr.WrapErr(constant.ErrCoze, err)
	}

	return &chatCompletion, contentItems, nil
}

// RecognizeAnswerSheetContent 识别答题卡内容
func RecognizeAnswerSheetContent(ctx context.Context, currentFileURL string, nextFileURL string) (any, []*model.ContentItem, error) {
	logs := logger.GetLogger(ctx)

	client := GetOrInitVolcengineClient()
	// client := GetOrInitBailianClient()
	exampleOutput, _ := jsoniter.MarshalToString(exampleAnswerSheetContentResult)

	// 构建系统提示
	systemPrompt := fmt.Sprintf(`# 角色
你是一个专业的答题卡识别助手，专注于精准识别传入的学生答题卡图片，并将其转换为结构化的内容。

## 技能
### 技能 1: 识别答题卡内容
1. 当接收到答题卡图像时，仔细分析图像内容，识别出所有大题和小题。
2. 对于每个大题，提取其中的所有小题，包括题号和学生作答内容。如果一个大题没有小题，也要创建一个子项，但题号为空字符串。
3. 如果当前页面的最后一道题跨页时，则从下一页中获取获取当前页面最后一题的剩余回答；否则只输出当前页面的作答内容。
4. 如果当前页面的最上方的题目不完整时，则不输出这道不完整的题目，只从第一个完整的题目开始输出。
5. 一道独立的题目就是一道大题，例如一道选择题、填空题、问答题等。而大题下面的每个小问则对应一道小题
6. 一道小题中有多个空时，用分隔符区分多个空的回答
7. 学生已经杠掉的回答，不应该被算作学生回答

## 输出格式
输出为一个数组，每个数组项代表一个大题，每个大题包含：
- number: 大题题号，如"一"、"二"、"I"、"II"等
- sub_items: 小题数组，每个小题包含：
  - number: 小题题号，如果没有小题则为空字符串
  - score: 这道题的分值，如果试卷上标注了分值则提取，否则填-1
  - answer: 学生作答内容

## 限制:
- 只专注于识别和反馈答题卡内容，拒绝回答无关话题。
- 所输出的内容必须按照要求整理成特定格式的数组项，不能偏离要求。
- %s一个参考的 json 格式: %s`, constant.OutputJSONPrompt, exampleOutput)

	// 构建消息
	messages := []openai.ChatCompletionMessageParamUnion{
		openai.SystemMessage(systemPrompt),
	}

	// 添加当前页图片
	messages = append(messages, openai.UserMessage([]openai.ChatCompletionContentPartUnionParam{
		{OfText: &openai.ChatCompletionContentPartTextParam{
			Text: "## 当前答题卡图片（需要识别的页面）\n",
		}},
		{OfImageURL: &openai.ChatCompletionContentPartImageParam{
			ImageURL: openai.ChatCompletionContentPartImageImageURLParam{
				URL:    currentFileURL,
				Detail: "high",
			},
		}},
	}))

	// 添加下一页图片（如果有）
	if nextFileURL != "" {

		messages = append(messages, openai.UserMessage([]openai.ChatCompletionContentPartUnionParam{
			{OfText: &openai.ChatCompletionContentPartTextParam{
				Text: "下一页答题卡图片（仅作参考，不需要识别）\n",
			}},
			{OfImageURL: &openai.ChatCompletionContentPartImageParam{
				ImageURL: openai.ChatCompletionContentPartImageImageURLParam{
					URL:    nextFileURL,
					Detail: "high",
				},
			}},
		}))
	}

	// 添加输出提示
	messages = append(messages, openai.UserMessage("# 输出\n\n请输出识别结果："))

	// 调用API
	modelName := viper.GetString("volcengine.ark.model.visual")
	// modelName := viper.GetString("aliyun.bailian.model.visual-72b")
	chatCompletion, err := client.Chat.Completions.New(ctx, openai.ChatCompletionNewParams{
		Messages: messages,
		Model:    modelName,
		ResponseFormat: openai.ChatCompletionNewParamsResponseFormatUnion{
			OfText: &shared.ResponseFormatTextParam{
				Type: "json_object",
			},
		},
	})
	if err != nil {
		logs.WithError(err).Errorf("fail to create chat completion")
		return nil, nil, bizerr.WrapErr(constant.ErrCoze, err)
	}
	logs.Infof("result: %+v", utils.ToJson(chatCompletion))

	// 记录大模型消耗
	if chatCompletion.Usage.TotalTokens > 0 {
		usageData := appModel.LLMUsageData{
			Model:        modelName,
			InputTokens:  chatCompletion.Usage.PromptTokens,
			OutputTokens: chatCompletion.Usage.CompletionTokens,
			TotalTokens:  chatCompletion.Usage.TotalTokens,
			Context:      "Answer sheet recognition",
			RawResponse:  utils.ToJson(chatCompletion),
		}

		// 记录额外信息
		extra := map[string]string{
			"has_next_page": fmt.Sprintf("%t", nextFileURL != ""),
		}

		// 调用记录函数
		costControlInfo := &model.CostControlInfo{
			RecordType: constant.RecordTypeLLMAnswerSheetRecognition,
			TaskID:     "",
			UserID:     "",
		}
		if err := RecordCostControlInfo(ctx, costControlInfo, usageData, extra); err != nil {
			logs.WithError(err).Warn("Failed to record LLM usage data")
		}
	}

	contentItems := make([]*model.ContentItem, 0)
	if err := utils.JSON.UnmarshalFromString(chatCompletion.Choices[0].Message.Content, &contentItems); err != nil {
		logs.WithError(err).Errorf("fail to unmarshal content items, resp: %+v", chatCompletion)
		return &chatCompletion, nil, bizerr.WrapErr(constant.ErrCoze, err)
	}

	return &chatCompletion, contentItems, nil
}
