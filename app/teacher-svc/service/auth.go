package service

import (
	"context"
	"time"

	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/teacher-svc/constant"
	"codeup.aliyun.com/level-up/public/common/bizerr"
	rclient "codeup.aliyun.com/level-up/public/common/clients/redis"
	"codeup.aliyun.com/level-up/public/common/logger"
	"github.com/redis/go-redis/v9"
	"golang.org/x/crypto/bcrypt"
)

func ValidatePassword(ctx context.Context, userId, inputPassword, password string) (bool, error) {
	logs := logger.GetLogger(ctx)
	// first check if attempts are exceeded
	key := rclient.GetUserTokenKey(constant.AuthPasswordLimitPrefix, userId)
	redisClient := rclient.GetRedisClient()
	attempts, err := redisClient.Get(ctx, key).Int()
	if err != nil && err != redis.Nil {
		logs.WithError(err).Errorf("fail to get key %s", key)
		return false, err
	}

	if attempts >= constant.MaxPasswordAttempts {
		return false, bizerr.NewBizErrf(constant.ErrAuth, "too many attempts, please try again later")
	}

	if err := bcrypt.CompareHashAndPassword([]byte(password), []byte(inputPassword)); err != nil {
		if err == bcrypt.ErrMismatchedHashAndPassword {
			pipe := redisClient.Pipeline()
			// Increment attempts
			if err := pipe.Incr(ctx, key).Err(); err != nil {
				logs.WithError(err).Errorf("fail to incr key %s", key)
				return false, err
			}
			// Set expiration time A day
			if err := pipe.Expire(ctx, key, time.Hour*24).Err(); err != nil {
				logs.WithError(err).Errorf("fail to set key %s", key)
				return false, err
			}
			if _, err := pipe.Exec(ctx); err != nil {
				logs.WithError(err).Errorf("fail to exec pipeline for key %s", key)
				return false, err
			}
			return false, nil
		} else {
			return false, err
		}
	}

	return true, nil
}
