package service

import (
	"bytes"
	"context"
	"io"
	"net/http"
	"time"

	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/teacher-svc/constant"
	"codeup.aliyun.com/level-up/public/common/bizerr"
	"codeup.aliyun.com/level-up/public/common/clients/aws"
	"codeup.aliyun.com/level-up/public/common/logger"
	"github.com/aws/aws-sdk-go-v2/service/s3"
	"github.com/spf13/viper"
)

const (
	S3ExpireTime = time.Hour * 24 * 7
)

// GetOrDownloadFile 检查文件是否存在，不存在则下载并上传，然后签发URL
// 参数：
// - ctx: 上下文
// - fileID: 文件ID
// - fileURL: 文件URL
// 返回：
// - string: 签名后的文件URL
// - error: 错误信息
func GetOrDownloadFile(ctx context.Context, fileID string, fileURL string) (string, error) {
	logs := logger.GetLogger(ctx)

	// 获取S3客户端
	client := aws.GetS3Client()
	uploader := aws.GetS3Uploader()

	// 获取存储桶名称
	bucketName := viper.GetString("volcengine.tos.bucket.default")

	// 检查文件是否存在
	_, err := client.HeadObject(ctx, &s3.HeadObjectInput{
		Bucket: &bucketName,
		Key:    &fileID,
	})

	// 如果文件不存在，则下载并上传
	if err != nil {
		// 记录错误并继续下载
		logs.WithError(err).Infof("file %s may not exist in S3, attempting to download", fileID)
		logs.Infof("downloading from URL %s", fileURL)

		// 下载文件
		resp, err := http.Get(fileURL)
		if err != nil {
			logs.WithError(err).Errorf("fail to download file from url: %s", fileURL)
			return "", bizerr.WrapErr(constant.ErrServer, err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			logs.Errorf("fail to download file, status code: %d, url: %s", resp.StatusCode, fileURL)
			return "", bizerr.NewBizErrf(constant.ErrServer, "download file failed with status: "+resp.Status)
		}

		// 读取文件内容
		bodyBytes, err := io.ReadAll(resp.Body)
		if err != nil {
			logs.WithError(err).Errorf("fail to read file data from url: %s", fileURL)
			return "", bizerr.WrapErr(constant.ErrServer, err)
		}

		// 获取Content-Type
		contentType := resp.Header.Get("Content-Type")

		// 上传到S3
		_, err = uploader.Upload(ctx, &s3.PutObjectInput{
			Bucket:      &bucketName,
			Key:         &fileID,
			Body:        bytes.NewReader(bodyBytes),
			ContentType: &contentType,
		})
		if err != nil {
			logs.WithError(err).Errorf("fail to upload file to S3: %s", fileID)
			return "", bizerr.WrapErr(constant.ErrServer, err)
		}

		logs.Infof("file %s uploaded to S3 successfully", fileID)
	} else {
		logs.Infof("file %s already exists in S3", fileID)
	}

	// 使用AWS S3客户端签发预签名URL
	presignClient := s3.NewPresignClient(client)

	// 创建GetObject请求
	request, err := presignClient.PresignGetObject(ctx, &s3.GetObjectInput{
		Bucket: &bucketName,
		Key:    &fileID,
	}, func(opts *s3.PresignOptions) {
		opts.Expires = S3ExpireTime
	})

	if err != nil {
		logs.WithError(err).Errorf("fail to presign S3 URL for %s", fileID)
		return "", bizerr.WrapErr(constant.ErrServer, err)
	}

	logs.Infof("presigned URL generated successfully for %s", fileID)
	return request.URL, nil
}
