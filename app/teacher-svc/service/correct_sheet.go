package service

import (
	"context"
	"fmt"
	"time"

	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/teacher-svc/constant"
	appModel "codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/teacher-svc/model"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/model"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/utils"
	"codeup.aliyun.com/level-up/public/common/bizerr"
	"codeup.aliyun.com/level-up/public/common/logger"
	commonutils "codeup.aliyun.com/level-up/public/common/utils"
	"github.com/openai/openai-go"
	"github.com/openai/openai-go/option"
	"github.com/spf13/viper"
)

const (
	CorrectSheetTimeout = 8 * time.Minute
)

// var exampleCorrectResult = map[string]any{
// "student_number": "string, 试卷上的学号信息，例如 19331288",
// "answer": []map[string]any{
// {
// "number": "string, 题号，如果题目有小题则格式为：大题(小题)，例如1(2)表示第1大题的第2小题",
// "score":  "float, 这道题的得分，参考答案没有给出分数则填-1",
// "answer": "string, 学生在答题卡上的答案完整内容",
// "reason": "string, 按点给分的题目中，需要给出给分理由及标准答案(不含解析)",
// "right":  "float, 必填，0-1 的数，代表当前学生作答与参考答案的匹配程度",
// "top_left": map[string]any{
// "x": "int, 作答区域左上角x坐标",
// "y": "int, 作答区域左上角y坐标",
// },
// "bottom_right": map[string]any{
// "x": "int, 作答区域右下角x坐标",
// "y": "int, 作答区域右下角y坐标",
// },
// },
// },
// }
// var parseCorrectResultSchema = commonutils.GenerateSchema[model.LLMCorrectResult]()

func CorrectSheet(ctx context.Context, info *model.CostControlInfo, sheetURLs []string, answerURLs []string) (any, *model.CorrectResult, error) {
	logs := logger.GetLogger(ctx)
	// byteParseCorrectResultSchema, _ := parseCorrectResultSchema.MarshalJSON()
	// strParseCorrectResultSchema := string(byteParseCorrectResultSchema)
	client := GetOrInitVolcengineClient()
	// client := GetOrInitBailianClient()
	// 构建系统提示
	systemPrompt := fmt.Sprintf(`# 角色
你是一名专业且严谨的老师，精通各个学科知识，你的工作是对照答案，对学生的答题卡进行逐题细致批改和准确判分。

## 技能
### 技能 1: 全科批改
1. 接收用户传入的参考答案图片和学生全部答题卡图片, 仔细分析答题卡图片内容，识别出学生的作答内容。
2. 按照答题卡上题目的顺序，逐题对比参考答案与学生在答题卡上的答案，严格依据参考答案的内容对答题卡上的答题结果进行判分。
3. 对试卷批改内容输出内容包括:
3.1 大题题号和小题题号，例如一道数学应用题可能由多道小问组成，此时的题号则为"12(1)".但是要注意试卷上的"一、选择题"这种不算大题，大题题号代表的是一道题，而不是一类题型的组合
3.2 学生作答与答案的匹配程度，是一个0-1之间的浮点数，0表示答案完全不匹配，1表示完全匹配，部分点答对则用小数代表匹配程度，越大代表匹配程度越高
3.3 学生该题的最终得分，是一个浮点数，参考答案没有给出分数则固定填-1
3.4 如果参考答案有给出判分规则，则需要根据判分规则，逐点给出判分依据, 但是对选择、填空等批改规则很明确的题目，则不需要给出判分依据
4. 如果答题卡上明确有写出学号信息的话，那么需要在输出中带上学号信息。
5. 如果答题卡包含了多个不同学生的学号的话，说明这张答题卡包含了多位学生的作答，此时不需要进行批改，只需要输出："这张答题卡包含了多位学生的作答，无法进行批改"即可
6. 你只需要对照着参考答案，核对答题卡作答和参考答案给出的内容或解释说明是否匹配，不需要关心参考答案本身的正确与否。

## 限制:
- 仅围绕全科题目批改相关内容进行回复，不回答其他无关问题。
- 学生的每一道作答，你都需要进行批改，不可遗漏或跳过`,
	)

	// 构建消息
	messages := []openai.ChatCompletionMessageParamUnion{
		openai.SystemMessage(systemPrompt),
	}

	// 添加参考答案
	answerPrompt := fmt.Sprint("# 输入\n## 参考答案\n")
	messages = append(messages, openai.UserMessage(answerPrompt))
	for _, answerURL := range answerURLs {
		messages = append(messages, openai.UserMessage([]openai.ChatCompletionContentPartUnionParam{
			{OfImageURL: &openai.ChatCompletionContentPartImageParam{
				ImageURL: openai.ChatCompletionContentPartImageImageURLParam{
					URL:    answerURL,
					Detail: "low",
				},
			}},
		}))
	}

	// 添加学生答题卡图片
	messages = append(messages, openai.UserMessage([]openai.ChatCompletionContentPartUnionParam{
		{OfText: &openai.ChatCompletionContentPartTextParam{
			Text: "## 学生答题卡图片\n",
		}},
	}))
	for _, sheetURL := range sheetURLs {
		messages = append(messages, openai.UserMessage([]openai.ChatCompletionContentPartUnionParam{
			{OfImageURL: &openai.ChatCompletionContentPartImageParam{
				ImageURL: openai.ChatCompletionContentPartImageImageURLParam{
					URL:    sheetURL,
					Detail: "low",
				},
			}},
		}))
	}

	// 添加输出提示
	messages = append(messages, openai.UserMessage(`# 输出
*注意：括号内的是解释说明的内容，实际输出时参考要求和样例输出*
## 参考输出1：一个完整的输出
==========
学号：19331288
题号：1
得分：5
匹配程度：1.0
判分依据：(这里不输出是因为这是一道选择题，不需要依据)

题号：2(1)
得分：3
匹配程度：0.5
判分依据：(实际输出时需要基于参考答案，按点给出判分的依据)
==========

## 参考输出2：一张答题卡包含了多位学生的作答
==========
这张答题卡包含了多位学生的作答，无法进行批改
==========

## 批改结果
你的批改结果是：
`))

	isLocal := commonutils.IsLocal()
	var (
		vlmRawOutput     any
		vlmOutputContent string
	)
	// 调用API
	modelName := viper.GetString("volcengine.ark.model.visual")
	// modelName := viper.GetString("aliyun.bailian.model.visual-72b")
	if !isLocal {
		chatCompletion, err := client.Chat.Completions.New(ctx, openai.ChatCompletionNewParams{
			Messages: messages,
			Model:    modelName,
		}, option.WithJSONSet("thinking", map[string]any{
			"type": "disabled", // 不使用深度思考能力
		}), option.WithRequestTimeout(CorrectSheetTimeout))
		if err != nil {
			logs.WithError(err).Errorf("fail to create chat completion")
			return nil, nil, bizerr.WrapErr(constant.ErrCoze, err)
		}
		logs.Infof("result: %+v", utils.ToJson(chatCompletion))
		vlmRawOutput = chatCompletion
		vlmOutputContent = chatCompletion.Choices[0].Message.Content

		// 记录大模型消耗
		if chatCompletion.Usage.TotalTokens > 0 {
			usageData := appModel.LLMUsageData{
				Model:        modelName,
				InputTokens:  chatCompletion.Usage.PromptTokens,
				OutputTokens: chatCompletion.Usage.CompletionTokens,
				TotalTokens:  chatCompletion.Usage.TotalTokens,
				Context:      "Image correction",
				RawResponse:  utils.ToJson(chatCompletion),
			}

			extra := map[string]string{
				"raw_resp": utils.ToJson(chatCompletion),
			}
			// 调用记录函数
			if err := RecordCostControlInfo(ctx, info, usageData, extra); err != nil {
				logs.WithError(err).Warn("Failed to record LLM usage data")
			}
		}
	} else {
		resp := client.Chat.Completions.NewStreaming(ctx, openai.ChatCompletionNewParams{
			Messages: messages,
			Model:    modelName,
		}, option.WithJSONSet("thinking", map[string]any{
			"type": "disabled", // 不使用深度思考能力
		}))
		for resp.Next() {
			data := resp.Current()
			content := data.Choices[0].Delta.Content
			fmt.Print(content)
			vlmOutputContent += content
		}
		vlmRawOutput = vlmOutputContent
	}

	// 文本表述转结构体
	parseSystemPromt := fmt.Sprintf(`# 角色
你是一名专业的结构化数据解析助手，负责将输入的文本转换为特定的结构化数据格式。
## 技能
### 技能1：将批改结果转成结构化数据
1. 当接收到一段文本时，仔细分析文本内容，将其转换为特定的结构化数据格式。
2. 输出的内容必须严格按照要求的格式进行，不得随意变动。
3. 输出的内容必须是合法的json格式，不需要包含任何其他内容，不要包含任何解释说明，不需要有markdown的标识。
4. 字段说明如下：
4.1 student_number: string, 学生的学号信息
4.2 multi_student_number: bool, 是否有多个学号，如果有多个学号则为true，否则为false
4.3 answer: array, 作答结果数组
4.4 answer[i].main_question: string, 大题题号，例如1
4.5 answer[i].sub_question: string, 小题题号，例如(2)，如果没有小题则为空字符串
4.6 answer[i].score: float, 这道题学生的最终得分，未说明则固定填-1
4.7 answer[i].reason: string, 给分理由，可能为空
4.8 answer[i].right: float, 必填，0-1 的数，代表当前学生作答与参考答案的匹配程度

## 限制:
- 只专注于将输入的文本转换为特定的结构化数据格式，拒绝回答其他无关话题。
- 所输出的内容必须按照要求整理成特定格式的数组项，不能偏离要求。`)
	parseMessages := []openai.ChatCompletionMessageParamUnion{
		openai.SystemMessage(parseSystemPromt),
	}
	parseMessages = append(parseMessages, openai.UserMessage([]openai.ChatCompletionContentPartUnionParam{
		{OfText: &openai.ChatCompletionContentPartTextParam{
			Text: "## 输入\n" + vlmOutputContent,
		}},
	}))
	parseMessages = append(parseMessages, openai.UserMessage("# 输出\n\n你的输出是："))

	// 调用API
	modelName = viper.GetString("volcengine.ark.model.visual-lite")
	// respFormat := openai.ChatCompletionNewParamsResponseFormatUnion{
	// 	OfJSONSchema: &shared.ResponseFormatJSONSchemaParam{
	// 		Type: "json_schema",
	// 		JSONSchema: shared.ResponseFormatJSONSchemaJSONSchemaParam{
	// 			Name:        "correct-result",
	// 			Description: openai.String("基于输入解析出的学生作答结果"),
	// 			Schema:      parseCorrectResultSchema,
	// 			// Strict:      openai.Bool(true),
	// 		},
	// 	},
	// }
	var parseOutput string
	if !isLocal {
		pareChatCompletion, err := client.Chat.Completions.New(ctx, openai.ChatCompletionNewParams{
			Messages: parseMessages,
			Model:    modelName,
			// ResponseFormat: respFormat,
		}, option.WithJSONSet("thinking", map[string]any{
			"type": "disabled",
		}), option.WithRequestTimeout(CorrectSheetTimeout))
		if err != nil {
			logs.WithError(err).Errorf("fail to create chat completion")
			return nil, nil, bizerr.WrapErr(constant.ErrCoze, err)
		}
		parseOutput = pareChatCompletion.Choices[0].Message.Content
		logs.Infof("parse result: %+v", utils.ToJson(pareChatCompletion))
		if pareChatCompletion.Usage.TotalTokens > 0 {
			usageData := appModel.LLMUsageData{
				Model:        modelName,
				InputTokens:  pareChatCompletion.Usage.PromptTokens,
				OutputTokens: pareChatCompletion.Usage.CompletionTokens,
				TotalTokens:  pareChatCompletion.Usage.TotalTokens,
				Context:      "Image correction parse",
				RawResponse:  utils.ToJson(pareChatCompletion),
			}
			if err := RecordCostControlInfo(ctx, info, usageData, map[string]string{
				"raw_resp": utils.ToJson(pareChatCompletion),
			}); err != nil {
				logs.WithError(err).Warn("Failed to record LLM usage data")
			}
		}
	} else {
		resp := client.Chat.Completions.NewStreaming(ctx, openai.ChatCompletionNewParams{
			Messages: parseMessages,
			Model:    modelName,
			// ResponseFormat: respFormat,
		}, option.WithJSONSet("thinking", map[string]any{
			"type": "disabled",
		}))
		for resp.Next() {
			data := resp.Current()
			content := data.Choices[0].Delta.Content
			fmt.Print(content)
			parseOutput += content
		}
		if resp.Err() != nil {
			logs.WithError(resp.Err()).Errorf("fail to create chat completion")
			return nil, nil, bizerr.WrapErr(constant.ErrCoze, resp.Err())
		}
	}

	var result model.CorrectResult
	if err := utils.JSON.UnmarshalFromString(parseOutput, &result); err != nil {
		logs.WithError(err).Errorf("fail to unmarshal question numbers, resp: %s", parseOutput)
		return vlmRawOutput, nil, bizerr.WrapErr(constant.ErrCoze, err)
	}

	return vlmRawOutput, &result, nil
}
