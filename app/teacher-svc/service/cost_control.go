package service

import (
	"context"
	"time"

	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/teacher-svc/constant"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/model"
	"codeup.aliyun.com/level-up/public/common/bizerr"
	"codeup.aliyun.com/level-up/public/common/clients/gorm"
	"codeup.aliyun.com/level-up/public/common/logger"
	uuid "github.com/satori/go.uuid"
)

// RecordCostControlInfo records cost control information for analysis
// Parameters:
// - ctx: context
// - info: basic cost control information including record type, task ID, and user ID
// - data: any data to be recorded (will be serialized to JSON)
// - extra: additional information as key-value pairs
// Returns:
// - error: any error that occurred
func RecordCostControlInfo(ctx context.Context, info *model.CostControlInfo, data any, extra map[string]string) error {
	logs := logger.GetLogger(ctx)

	// Get database connection
	db := gorm.GetMultiDB("default")

	// Parse UUIDs
	var taskUUID, userUUID uuid.UUID
	var err error

	if info.TaskID != "" {
		taskUUID, err = uuid.FromString(info.TaskID)
		if err != nil {
			logs.WithError(err).Errorf("Invalid task ID: %s", info.TaskID)
			return bizerr.WrapErr(constant.ErrParam, err)
		}
	}

	if info.UserID != "" {
		userUUID, err = uuid.FromString(info.UserID)
		if err != nil {
			logs.WithError(err).Errorf("Invalid user ID: %s", info.UserID)
			return bizerr.WrapErr(constant.ErrParam, err)
		}
	}

	// Create record
	record := &model.CostControlRecord{
		Type:       info.RecordType,
		TaskID:     taskUUID,
		UserID:     userUUID,
		RecordedAt: time.Now(),
	}

	// Set data
	if data != nil {
		if err := record.SetData(data); err != nil {
			logs.WithError(err).Error("Failed to serialize data")
			return bizerr.WrapErr(constant.ErrParam, err)
		}
	}

	// Set extra
	if extra != nil {
		if err := record.SetExtra(extra); err != nil {
			logs.WithError(err).Error("Failed to serialize extra")
			return bizerr.WrapErr(constant.ErrParam, err)
		}
	}

	// Save to database
	if err := model.CreateCostControlRecord(db, record); err != nil {
		logs.WithError(err).Error("Failed to create cost control record")
		return bizerr.WrapErr(constant.ErrDB, err)
	}

	logs.Infof("Cost control record created: type=%s, taskID=%s, userID=%s", info.RecordType, info.TaskID, info.UserID)
	return nil
}
