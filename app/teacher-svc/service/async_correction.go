package service

import (
	"context"

	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/teacher-svc/constant"
	"codeup.aliyun.com/level-up/public/common/bizerr"
	"codeup.aliyun.com/level-up/public/common/logger"
	"github.com/go-resty/resty/v2"
	"github.com/spf13/viper"
	"gorm.io/gorm"
)

type MatchAndCorrectSheetRequest struct {
	ItemID  string `json:"item_id"`
	SheetID string `json:"sheet_id"`
}

// AsyncCorrectSheetOrItem processes a single upload queue item for correction
func AsyncCorrectSheetOrItem(ctx context.Context, db *gorm.DB, queueItemID, sheetID string) error {
	logs := logger.GetLogger(ctx)
	// 调用内部API进行批改
	innerHost := viper.GetString("inner.host")
	client := resty.New().SetBaseURL(innerHost)
	client = client.SetHeader("X-Fc-Invocation-Type", "Async")

	resp, err := client.R().
		SetBody(MatchAndCorrectSheetRequest{
			SheetID: sheetID,
			ItemID:  queueItemID,
		}).Post("/inner/v1/correction/run")
	if err != nil {
		logs.WithError(err).Errorf("fail to run item %s, sheet %s", queueItemID, sheetID)
		return bizerr.WrapErr(constant.ErrServer, err)
	}
	if resp.StatusCode() != 202 && resp.StatusCode() != 200 {
		logs.Errorf("fail to run item %s, sheet %s, status code %d, resp %s", queueItemID, sheetID, resp.StatusCode(), resp.String())
		return bizerr.WrapErr(constant.ErrServer, err)
	}

	return nil
}
