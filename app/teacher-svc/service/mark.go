package service

import (
	"os"
	"sync"

	"github.com/sirupsen/logrus"
	"golang.org/x/image/font"
	"golang.org/x/image/font/opentype"
)

var (
	correctFace         font.Face
	correctFaceInitOnce sync.Once
)

func LoadCorrectFace() font.Face {
	correctFaceInitOnce.Do(func() {
		fontFile, err := os.Open("data/alibaba普惠.ttf") // 需要有TTF字体文件
		if err != nil {
			logrus.WithError(err).Errorf("fail to open font file")
			return
		}
		// defer fontFile.Close()
		ttfFont, err := opentype.ParseReaderAt(fontFile)
		if err != nil {
			logrus.WithError(err).Errorf("fail to parse font file")
			return
		}

		// 设置字体大小（点大小，例如24pt）
		fontSize := 18.0
		face, err := opentype.NewFace(ttfFont, &opentype.FaceOptions{
			Size:    fontSize,
			DPI:     300, // 每英寸点数
			Hinting: font.HintingFull,
		})
		if err != nil {
			logrus.WithError(err).<PERSON><PERSON><PERSON>("fail to create font face")
			return
		}
		correctFace = face
	})
	return correctFace
}
