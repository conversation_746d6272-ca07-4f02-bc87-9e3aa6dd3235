package service

import (
	"context"
	"testing"

	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/model"
	"codeup.aliyun.com/level-up/public/common/clients/gorm"
	uuid "github.com/satori/go.uuid"
	"github.com/stretchr/testify/assert"
)

func TestRecordCostControlInfo(t *testing.T) {
	// Skip if not running in test environment
	t.Skip("Skipping test that requires database connection")

	// Initialize test context
	ctx := context.Background()

	// Generate test data
	testType := "test_type"
	testTaskID := uuid.NewV4().String()
	testUserID := uuid.NewV4().String()
	testData := map[string]any{
		"key1": "value1",
		"key2": 123,
	}
	testExtra := map[string]string{
		"source": "test",
		"env":    "development",
	}

	// Create cost control info
	costControlInfo := &model.CostControlInfo{
		RecordType: testType,
		TaskID:     testTaskID,
		UserID:     testUserID,
	}

	// Call the function
	err := RecordCostControlInfo(ctx, costControlInfo, testData, testExtra)
	assert.NoError(t, err)

	// Verify the record was created
	db := gorm.GetMultiDB("default")
	taskUUID, _ := uuid.FromString(testTaskID)
	records, err := model.GetCostControlRecordsByTaskID(db, taskUUID)
	assert.NoError(t, err)
	assert.GreaterOrEqual(t, len(records), 1)

	// Verify the last record has the correct data
	lastRecord := records[len(records)-1]
	assert.Equal(t, testType, lastRecord.Type)
	assert.Equal(t, taskUUID, lastRecord.TaskID)
}
