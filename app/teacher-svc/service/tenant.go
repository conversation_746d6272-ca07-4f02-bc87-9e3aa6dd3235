package service

import (
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/teacher-svc/constant"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/model"
	"codeup.aliyun.com/level-up/public/common/bizerr"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
)

func JoinTenant(db *gorm.DB, userId, tenantCode string) error {
	tenant, err := model.GetTenantByCode(db, tenantCode)
	if err != nil {
		return bizerr.WrapErr(constant.ErrDB, err)
	}

	// check if user already in tenant
	tenantUser, err := model.GetTenantUserByTenantAndUserID(db, tenant.UUID.String(), userId)
	if err != nil {
		if err != gorm.ErrRecordNotFound {
			return bizerr.WrapErr(constant.ErrDB, err)
		} else {
			tenantUser = nil
		}
	}

	if tenantUser != nil {
		// already in tenant
		return nil
	}
	// add user to tenant and set status to pendding
	userUUID, err := uuid.FromString(userId)
	if err != nil {
		return bizerr.WrapErr(constant.ErrParam, err)
	}
	tenantUser = &model.TenantUser{
		TenantID: tenant.UUID,
		UserID:   userUUID,
		Status:   model.TenantUserStatusPending,
	}
	err = model.CreateTenantUser(db, tenantUser)
	if err != nil {
		return bizerr.WrapErr(constant.ErrDB, err)
	}

	// get teacher user role
	role, err := model.GetRoleByCode(db, model.TenantRoleTeacher)
	if err != nil {
		return bizerr.WrapErr(constant.ErrDB, err)
	}

	// add user role
	tenantUserRole := &model.TenantUserRole{
		TenantUserID: tenantUser.UUID,
		TenantID:     tenant.UUID,
		UserID:       userUUID,
		RoleID:       role.UUID,
	}

	err = model.CreateTenantUserRole(db, tenantUserRole)
	if err != nil {
		return bizerr.WrapErr(constant.ErrDB, err)
	}

	return nil
}
