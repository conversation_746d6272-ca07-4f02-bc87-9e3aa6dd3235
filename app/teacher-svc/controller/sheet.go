package controller

import (
	"fmt"
	"net/http"
	"time"

	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/teacher-svc/constant"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/teacher-svc/service"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/model"
	"codeup.aliyun.com/level-up/public/common/bizerr"
	gormclient "codeup.aliyun.com/level-up/public/common/clients/gorm"
	"codeup.aliyun.com/level-up/public/common/logger"
	commonModel "codeup.aliyun.com/level-up/public/common/model"
	"codeup.aliyun.com/level-up/public/common/utils"
	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"github.com/gin-gonic/gin"
	"github.com/spf13/viper"
	"gorm.io/gorm"
)

type ListUploadQueueItemsRequest struct {
	Page int `form:"page"`
	Size int `form:"size"`
}

type UploadQueueItem struct {
	ItemID         string `json:"item_id"`
	TaskID         string `json:"task_id"` // 任务ID
	FileURL        string `json:"file_url"`
	SequenceNumber int    `json:"sequence_number"`
	AnswerFileURL  string `json:"answer_file_url"`
	MarkedFileURL  string `json:"marked_file_url"`
	StudentNumber  string `json:"student_number"`
}

// GroupCorrectionItemsRequest 分组请求
type GroupCorrectionItemsRequest struct {
	HasStudentNumber bool `json:"has_student_number"` // 是否每张答题卡都有学号信息
	ForceBySheets    bool `json:"force_by_sheets"`    // 是否强制按纸张数量分组
}

// SheetDTO 试卷信息
type SheetDTO struct {
	ID            string    `json:"id"`             // 试卷ID
	TaskID        string    `json:"task_id"`        // 任务ID
	StudentNumber string    `json:"student_number"` // 学号
	ItemCount     int       `json:"item_count"`     // 包含的上传队列项数量
	CreatedAt     time.Time `json:"created_at"`     // 创建时间

	Status        string               `json:"status"`                   // 批改状态
	CorrectResult *model.CorrectResult `json:"correct_result,omitempty"` // 批改详情

	SegmentationStatus string `json:"segmentation_status"` // 切题状态
}

// SaveSegmentationRequest 保存切题结果请求
type SaveSegmentationRequest struct {
	Segmentations []*model.CorrectResultItem `json:"segmentations"` // 切题结果列表
}

// ApplySegmentationRequest 应用切题结果请求
type ApplySegmentationRequest struct {
	ReferenceSheetID string `json:"reference_sheet_id"` // 参考试卷ID
}

func ListUploadQueueItems(c *gin.Context) {
	logs := logger.GetLogger(c)

	// 验证用户身份
	token := utils.GetCtxAuthToken(c)
	if token == nil {
		c.JSON(http.StatusUnauthorized, gin.H{})
		return
	}

	taskID := c.Param("taskID")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrParam, "任务ID不能为空")))
		return
	}
	var req ListUploadQueueItemsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		logs.WithError(err).Error("Failed to bind query parameters")
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrParam, err)))
		return
	}

	// 鉴权
	db := gormclient.GetMultiDB("")
	task, err := model.GetCorrectionTask(db, taskID)
	if err != nil {
		logs.WithError(err).Errorf("fail to get task %s, err %v", taskID, err)
		c.JSON(http.StatusInternalServerError, bizerr.WrapErr(constant.ErrDB, err))
		return
	}
	if task == nil || task.UserID.String() != token.UserID {
		logs.Infof("task %s not belong to user %s", taskID, token.UserID)
		c.JSON(http.StatusForbidden, gin.H{})
		return
	}

	// 获取批改列表
	offset, limit := utils.GetLimitOffset(req.Page, req.Size)
	items, total, err := model.GetUploadQueueItemsOfTask(db, taskID, limit, offset, true)
	if err != nil {
		logs.WithError(err).Errorf("fail to get items of task %s, err %v", taskID, err)
		c.JSON(http.StatusInternalServerError, bizerr.WrapErr(constant.ErrDB, err))
		return
	}
	resp := []*UploadQueueItem{}
	endpoint := viper.GetString("aliyun.oss.endpoint.public")
	bucketName := viper.GetString("aliyun.oss.bucket.default")
	for _, item := range items {
		fileURL, err := signOSSUrl(c, endpoint, bucketName, item.FileID, true, time.Hour, []oss.Option{
			oss.ContentDisposition("inline"),
		})
		if err != nil {
			logs.WithError(err).Errorf("fail to sign oss url %s", item.FileID)
		}
		queueItem := &UploadQueueItem{
			ItemID:  item.UUID.String(),
			TaskID:  taskID,
			FileURL: fileURL,
		}

		// 添加标记后的图片URL
		if item.MarkedFileID != "" {
			markedFileURL, err := signOSSUrl(c, endpoint, bucketName, item.MarkedFileID, true, time.Hour, []oss.Option{
				oss.ContentDisposition("inline"),
			})
			if err != nil {
				logs.WithError(err).Errorf("fail to sign oss url %s", item.MarkedFileID)
			} else {
				queueItem.MarkedFileURL = markedFileURL
			}
		}

		resp = append(resp, queueItem)
	}

	c.JSON(http.StatusOK, commonModel.NewSuccessListResponse(req.Page, limit, total, resp))
}

// GetSheets 获取试卷列表
func GetSheets(c *gin.Context) {
	logs := logger.GetLogger(c)

	// 验证用户身份
	token := utils.GetCtxAuthToken(c)
	if token == nil {
		c.JSON(http.StatusUnauthorized, gin.H{})
		return
	}

	// 获取任务ID
	taskID := c.Param("taskID")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrParam, "任务ID不能为空")))
		return
	}

	// 解析分页参数
	var req ListUploadQueueItemsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		logs.WithError(err).Error("Failed to bind query parameters")
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrParam, err)))
		return
	}

	// 获取任务
	db := gormclient.GetMultiDB("")
	task, err := model.GetCorrectionTask(db, taskID)
	if err != nil {
		logs.WithError(err).Error("获取批改任务失败")
		c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	if task == nil {
		c.JSON(http.StatusNotFound, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrNotFound, "任务不存在")))
		return
	}

	// 检查任务是否属于当前用户
	if task.UserID.String() != token.UserID {
		c.JSON(http.StatusForbidden, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrAuth, "无权访问此任务")))
		return
	}

	// 获取分页参数
	offset, limit := utils.GetLimitOffset(req.Page, req.Size)

	// 获取试卷列表
	sheets, total, err := model.GetSheetsByTaskID(db, taskID, limit, offset, true)
	if err != nil {
		logs.WithError(err).Error("获取试卷列表失败")
		c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	// 构建响应
	respList := make([]*SheetDTO, 0, len(sheets))

	for _, sheet := range sheets {
		// 获取试卷包含的上传队列项数量
		sheetItems, err := model.GetSheetItemsBySheetID(db, sheet.UUID.String())
		if err != nil {
			logs.WithError(err).Error("获取试卷项目失败")
			continue
		}

		respList = append(respList, &SheetDTO{
			ID:            sheet.UUID.String(),
			TaskID:        taskID,
			StudentNumber: sheet.StudentNumber,
			ItemCount:     len(sheetItems),
			CreatedAt:     sheet.CreatedAt,
		})
	}

	c.JSON(http.StatusOK, commonModel.NewSuccessListResponse(req.Page, limit, total, respList))
}

// GetSheetDetail 获取试卷详情
func GetSheetDetail(c *gin.Context) {
	logs := logger.GetLogger(c)

	// 验证用户身份
	token := utils.GetCtxAuthToken(c)
	if token == nil {
		c.JSON(http.StatusUnauthorized, gin.H{})
		return
	}

	// 获取试卷ID
	sheetID := c.Param("sheetID")
	if sheetID == "" {
		c.JSON(http.StatusBadRequest, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrParam, "ID不能为空")))
		return
	}

	// 获取试卷信息
	db := gormclient.GetMultiDB("")
	sheet, err := model.GetSheetByID(db, sheetID)
	if err != nil {
		logs.WithError(err).Errorf("fail to get sheet of %s", sheetID)
		c.JSON(http.StatusInternalServerError, bizerr.WrapErr(constant.ErrDB, err))
		return
	}
	if sheet == nil {
		c.JSON(http.StatusBadRequest, bizerr.NewBizErrf(constant.ErrParam, "sheet not found"))
		return
	}

	// 获取任务信息并鉴权
	task, err := model.GetCorrectionTask(db, sheet.TaskID.String())
	if err != nil {
		logs.WithError(err).Errorf("fail to get task %s", sheet.TaskID.String())
		c.JSON(http.StatusInternalServerError, bizerr.WrapErr(constant.ErrDB, err))
		return
	}
	if task == nil {
		c.JSON(http.StatusBadRequest, bizerr.NewBizErrf(constant.ErrParam, "task not found"))
		return
	}
	if task.UserID.String() != token.UserID {
		logs.Infof("sheet %s not belong to user %s", sheetID, token.UserID)
		c.JSON(http.StatusForbidden, gin.H{})
		return
	}

	// 获取试卷包含的上传队列项数量
	sheetItems, err := model.GetSheetItemsBySheetID(db, sheet.UUID.String())
	if err != nil {
		logs.WithError(err).Error("获取试卷项目失败")
		c.JSON(http.StatusInternalServerError, bizerr.WrapErr(constant.ErrDB, err))
		return
	}

	// 构建响应
	resp := &SheetDTO{
		ID:                 sheet.UUID.String(),
		TaskID:             sheet.TaskID.String(),
		StudentNumber:      sheet.StudentNumber,
		ItemCount:          len(sheetItems),
		CreatedAt:          sheet.CreatedAt,
		Status:             sheet.Status,
		CorrectResult:      sheet.GetCorrectResult(c, db),
		SegmentationStatus: sheet.SegmentationStatus,
	}

	c.JSON(http.StatusOK, commonModel.NewSuccessResponse(resp))
}

// GetSheetItems 获取试卷的上传队列项
func GetSheetItems(c *gin.Context) {
	logs := logger.GetLogger(c)

	// 验证用户身份
	token := utils.GetCtxAuthToken(c)
	if token == nil {
		c.JSON(http.StatusUnauthorized, gin.H{})
		return
	}

	// 获取试卷ID
	sheetID := c.Param("sheetID")
	if sheetID == "" {
		c.JSON(http.StatusBadRequest, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrParam, "ID不能为空")))
		return
	}

	// 解析分页参数
	var req ListUploadQueueItemsRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		logs.WithError(err).Error("Failed to bind query parameters")
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrParam, err)))
		return
	}

	// 获取试卷信息
	db := gormclient.GetMultiDB("")
	sheet, err := model.GetSheetByID(db, sheetID)
	if err != nil {
		logs.WithError(err).Errorf("fail to get sheet of %s", sheetID)
		c.JSON(http.StatusInternalServerError, bizerr.WrapErr(constant.ErrDB, err))
		return
	}
	if sheet == nil {
		c.JSON(http.StatusBadRequest, bizerr.NewBizErrf(constant.ErrParam, "sheet not found"))
		return
	}

	// 获取任务信息并鉴权
	task, err := model.GetCorrectionTask(db, sheet.TaskID.String())
	if err != nil {
		logs.WithError(err).Errorf("fail to get task %s", sheet.TaskID.String())
		c.JSON(http.StatusInternalServerError, bizerr.WrapErr(constant.ErrDB, err))
		return
	}
	if task == nil {
		c.JSON(http.StatusBadRequest, bizerr.NewBizErrf(constant.ErrParam, "task not found"))
		return
	}
	if task.UserID.String() != token.UserID {
		logs.Infof("sheet %s not belong to user %s", sheetID, token.UserID)
		c.JSON(http.StatusForbidden, gin.H{})
		return
	}

	// 获取分页参数
	offset, limit := utils.GetLimitOffset(req.Page, req.Size)

	// 获取试卷的上传队列项
	sheetItems, total, err := model.GetSheetItems(db, sheet.UUID.String(), limit, offset, true)
	if err != nil {
		logs.WithError(err).Error("获取试卷项目失败")
		c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	// 获取上传队列项详情
	itemIDs := make([]string, 0, len(sheetItems))
	for _, item := range sheetItems {
		itemIDs = append(itemIDs, item.QueueItemID.String())
	}

	queueItems, err := model.GetUploadQueueItemsByIDs(db, itemIDs)
	if err != nil {
		logs.WithError(err).Errorf("fail to get queue items of %v", itemIDs)
		c.JSON(http.StatusInternalServerError, bizerr.WrapErr(constant.ErrDB, err))
		return
	}
	service.SortSheetItems(c, queueItems)

	// 构建响应
	respList := make([]*UploadQueueItem, 0, len(queueItems))
	endpoint := viper.GetString("aliyun.oss.endpoint.public")
	bucketName := viper.GetString("aliyun.oss.bucket.default")

	// 创建上传队列项ID到对象的映射
	queueItemMap := make(map[string]*model.UploadQueueItem)
	for _, item := range queueItems {
		queueItemMap[item.UUID.String()] = item
	}

	// 按照sheetItems的顺序构建响应
	for _, sheetItem := range sheetItems {
		item, ok := queueItemMap[sheetItem.QueueItemID.String()]
		if !ok {
			continue
		}

		fileURL, err := signOSSUrl(c, endpoint, bucketName, item.FileID, true, time.Hour, []oss.Option{
			oss.ContentDisposition("inline"),
		})
		if err != nil {
			logs.WithError(err).Errorf("fail to sign oss url %s", item.FileID)
		}

		queueItem := &UploadQueueItem{
			ItemID:         item.UUID.String(),
			TaskID:         sheet.TaskID.String(),
			SequenceNumber: item.Sequence,
			FileURL:        fileURL,
		}

		if item.AnswerFileID != "" {
			answerFileURL, err := signOSSUrl(c, endpoint, bucketName, item.AnswerFileID, true, time.Hour, []oss.Option{
				oss.ContentDisposition("inline"),
			})
			if err != nil {
				logs.WithError(err).Errorf("fail to sign oss url %s", item.AnswerFileID)
			}
			queueItem.AnswerFileURL = answerFileURL
		}

		// 添加标记后的图片URL
		if item.MarkedFileID != "" {
			markedFileURL, err := signOSSUrl(c, endpoint, bucketName, item.MarkedFileID, true, time.Hour, []oss.Option{
				oss.ContentDisposition("inline"),
			})
			if err != nil {
				logs.WithError(err).Errorf("fail to sign oss url %s", item.MarkedFileID)
			} else {
				queueItem.MarkedFileURL = markedFileURL
			}
		}

		respList = append(respList, queueItem)
	}

	c.JSON(http.StatusOK, commonModel.NewSuccessListResponse(req.Page, limit, total, respList))
}

// SaveSheetSegmentation 保存用户对指定项目进行切题的结果
func SaveSheetSegmentation(c *gin.Context) {
	logs := logger.GetLogger(c)

	// 验证用户身份
	token := utils.GetCtxAuthToken(c)
	if token == nil {
		c.JSON(http.StatusUnauthorized, gin.H{})
		return
	}

	// 获取sheet ID和item ID
	sheetID := c.Param("sheetID")
	if sheetID == "" {
		c.JSON(http.StatusBadRequest, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrParam, "试卷ID不能为空")))
		return
	}

	itemID := c.Param("itemID")
	if itemID == "" {
		c.JSON(http.StatusBadRequest, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrParam, "项目ID不能为空")))
		return
	}

	// 解析请求参数
	var req SaveSegmentationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logs.WithError(err).Error("解析切题参数失败")
		c.JSON(http.StatusBadRequest, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrParam, err)))
		return
	}

	// 获取试卷信息
	db := gormclient.GetMultiDB("")
	sheet, err := model.GetSheetByID(db, sheetID)
	if err != nil {
		logs.WithError(err).Errorf("fail to get sheet of %s", sheetID)
		c.JSON(http.StatusInternalServerError, bizerr.WrapErr(constant.ErrDB, err))
		return
	}
	if sheet == nil {
		c.JSON(http.StatusBadRequest, bizerr.NewBizErrf(constant.ErrParam, "sheet not found"))
		return
	}

	// 获取任务信息并鉴权
	task, err := model.GetCorrectionTask(db, sheet.TaskID.String())
	if err != nil {
		logs.WithError(err).Errorf("fail to get task %s", sheet.TaskID.String())
		c.JSON(http.StatusInternalServerError, bizerr.WrapErr(constant.ErrDB, err))
		return
	}
	if task == nil {
		c.JSON(http.StatusBadRequest, bizerr.NewBizErrf(constant.ErrParam, "task not found"))
		return
	}
	if task.UserID.String() != token.UserID {
		logs.Infof("sheet %s not belong to user %s", sheetID, token.UserID)
		c.JSON(http.StatusForbidden, gin.H{})
		return
	}

	// 开始事务
	err = db.Transaction(func(tx *gorm.DB) error {
		// 获取当前项目的CorrectResult
		correctResult := sheet.GetCorrectResult(c, tx)
		if correctResult == nil {
			// 如果没有批改结果，返回错误
			return fmt.Errorf("项目没有批改结果")
		}

		// 更新每个答案的坐标
		for _, answer := range correctResult.Answer {
			// 查找匹配的切题结果
			for _, seg := range req.Segmentations {
				if answer.Number() == seg.Number() {
					// 更新坐标
					answer.TopLeft = seg.TopLeft
					answer.BottomRight = seg.BottomRight
					answer.ItemID = itemID
					break
				}
			}
		}

		// 保存更新后的CorrectResult
		if err := sheet.SetCorrectResult(c, tx, correctResult); err != nil {
			logs.WithError(err).Errorf("fail to set correct result for sheet %s", sheet.UUID.String())
			return err
		}
		if err := model.UpdateSheet(tx, sheet.UUID.String(), map[string]interface{}{
			"correction_result_id": sheet.CorrectionResultID,
			"segmentation_status":  model.SegmentationStatusManual,
		}); err != nil {
			logs.WithError(err).Errorf("fail to update sheet %s", sheet.UUID.String())
			return err
		}

		return nil
	})

	if err != nil {
		logs.WithError(err).Error("保存切题结果失败")
		c.JSON(http.StatusInternalServerError, bizerr.WrapErr(constant.ErrDB, err))
		return
	}

	c.JSON(http.StatusOK, commonModel.NewSuccessResponse(gin.H{
		"message": "切题结果保存成功",
	}))
}

// ApplySheetSegmentation 应用切题结果到所有试卷
func ApplySheetSegmentation(c *gin.Context) {
	logs := logger.GetLogger(c)

	// 验证用户身份
	token := utils.GetCtxAuthToken(c)
	if token == nil {
		c.JSON(http.StatusUnauthorized, gin.H{})
		return
	}

	// 获取任务ID
	taskID := c.Param("taskID")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrParam, "任务ID不能为空")))
		return
	}

	// 解析请求参数
	var req ApplySegmentationRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logs.WithError(err).Error("解析切题参数失败")
		c.JSON(http.StatusBadRequest, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrParam, err)))
		return
	}

	// 验证参考试卷ID
	if req.ReferenceSheetID == "" {
		c.JSON(http.StatusBadRequest, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrParam, "参考试卷ID不能为空")))
		return
	}

	// 获取任务信息
	db := gormclient.GetMultiDB("")
	task, err := model.GetCorrectionTask(db, taskID)
	if err != nil {
		logs.WithError(err).Error("获取批改任务失败")
		c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	if task == nil {
		c.JSON(http.StatusNotFound, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrNotFound, "任务不存在")))
		return
	}

	// 检查任务是否属于当前用户
	if task.UserID.String() != token.UserID {
		c.JSON(http.StatusForbidden, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrAuth, "无权访问此任务")))
		return
	}

	// 获取参考试卷
	referenceSheet, err := model.GetSheetByID(db, req.ReferenceSheetID)
	if err != nil {
		logs.WithError(err).Errorf("fail to get reference sheet of %s", req.ReferenceSheetID)
		c.JSON(http.StatusInternalServerError, bizerr.WrapErr(constant.ErrDB, err))
		return
	}
	if referenceSheet == nil {
		c.JSON(http.StatusBadRequest, bizerr.NewBizErrf(constant.ErrParam, "reference sheet not found"))
		return
	}

	// 检查参考试卷是否属于当前任务
	if referenceSheet.TaskID.String() != taskID {
		c.JSON(http.StatusBadRequest, bizerr.NewBizErrf(constant.ErrParam, "参考试卷不属于当前任务"))
		return
	}

	// 收集参考试卷的切题信息
	segmentationMap := make(map[string]*model.CorrectResultItem)
	refItemOrderMap := make(map[string]int) // item到第几张试卷的映射关系
	correctResult := referenceSheet.GetCorrectResult(c, db)
	if correctResult == nil {
		c.JSON(http.StatusBadRequest, bizerr.NewBizErrf(constant.ErrParam, "参考试卷没有切题信息"))
		return
	}
	refUploadSheetItems, err := model.GetUploadQueueItemsBySheetID(db, referenceSheet.UUID.String())
	if err != nil {
		logs.WithError(err).Errorf("fail to get upload queue items of %s", referenceSheet.UUID.String())
		c.JSON(http.StatusInternalServerError, bizerr.WrapErr(constant.ErrDB, err))
		return
	}
	service.SortSheetItems(c, refUploadSheetItems)
	for i, item := range refUploadSheetItems {
		refItemOrderMap[item.UUID.String()] = i
	}

	for idx, answer := range correctResult.Answer {
		if answer.TopLeft != nil && answer.BottomRight != nil {
			number := answer.Number()
			segmentationMap[number] = correctResult.Answer[idx]
		}
	}

	// 如果没有切题信息，返回错误
	if len(segmentationMap) == 0 {
		c.JSON(http.StatusBadRequest, bizerr.NewBizErrf(constant.ErrParam, "参考试卷没有切题信息"))
		return
	}

	// 获取当前执行ID下的所有试卷
	sheets, _, err := model.GetSheetsByTaskID(db, taskID, -1, 0, false)
	if err != nil {
		logs.WithError(err).Error("获取试卷列表失败")
		c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	// 处理每个试卷
	processedSheets := 0
	failedSheets := 0

	// 遍历所有试卷
	for _, sheet := range sheets {
		// 跳过参考试卷
		if sheet.UUID.String() == req.ReferenceSheetID {
			continue
		}
		// 找到试卷的item列表，以及顺序
		itemOrderIDMap := map[int]string{}
		uploadQueueItems, err := model.GetUploadQueueItemsBySheetID(db, sheet.UUID.String())
		if err != nil {
			logs.WithError(err).Errorf("fail to get upload queue items of %s", sheet.UUID.String())
			failedSheets++
			continue
		}
		service.SortSheetItems(c, uploadQueueItems)
		for i, item := range uploadQueueItems {
			itemOrderIDMap[i] = item.UUID.String()
		}

		// 为每个试卷创建单独的事务
		err = db.Transaction(func(tx *gorm.DB) error {
			// 获取当前项目的CorrectResult
			correctResult := sheet.GetCorrectResult(c, tx)
			if correctResult == nil {
				// 如果没有批改结果，跳过
				return nil
			}

			// 更新每个答案的坐标
			for _, answer := range correctResult.Answer {
				// 查找匹配的切题结果
				number := answer.Number()
				if seg, ok := segmentationMap[number]; ok {
					// 更新坐标
					answer.TopLeft = seg.TopLeft
					answer.BottomRight = seg.BottomRight
					// 更新大题和小题题号
					answer.MainQuestion = seg.MainQuestion
					answer.SubQuestion = seg.SubQuestion
					answer.ItemID = itemOrderIDMap[refItemOrderMap[seg.ItemID]]
				}
			}

			// 保存更新后的CorrectResult
			if err := sheet.SetCorrectResult(c, tx, correctResult); err != nil {
				logs.WithError(err).Errorf("fail to set correct result for sheet %s", sheet.UUID.String())
				return err
			}
			if err := model.UpdateSheet(tx, sheet.UUID.String(), map[string]interface{}{
				"correction_result_id": sheet.CorrectionResultID,
				"segmentation_status":  model.SegmentationStatusApplied,
			}); err != nil {
				logs.WithError(err).Errorf("fail to update sheet %s", sheet.UUID.String())
				return err
			}

			return nil
		})

		// 处理事务结果
		if err != nil {
			logs.WithError(err).Errorf("应用切题结果到试卷 %s 失败", sheet.UUID.String())
			failedSheets++
		} else {
			processedSheets++
		}
	}

	// 如果所有试卷都失败，返回错误
	if processedSheets == 0 && failedSheets > 0 {
		logs.Error("所有试卷应用切题结果都失败")
		c.JSON(http.StatusInternalServerError, bizerr.WrapErr(constant.ErrDB, fmt.Errorf("应用切题结果失败")))
		return
	}

	c.JSON(http.StatusOK, commonModel.NewSuccessResponse(gin.H{
		"message":          "切题结果应用成功",
		"processed_sheets": processedSheets,
		"failed_sheets":    failedSheets,
	}))
}

func GetUploadQueueItemDetail(c *gin.Context) {
	logs := logger.GetLogger(c)

	// 验证用户身份
	token := utils.GetCtxAuthToken(c)
	if token == nil {
		c.JSON(http.StatusUnauthorized, gin.H{})
		return
	}

	itemID := c.Param("itemID")
	if itemID == "" {
		c.JSON(http.StatusBadRequest, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrParam, "ID不能为空")))
		return
	}

	// 获取 item 信息
	db := gormclient.GetMultiDB("")
	items, err := model.GetUploadQueueItemsByIDs(db, []string{itemID})
	if err != nil {
		logs.WithError(err).Errorf("fail to get item of %s", itemID)
		c.JSON(http.StatusInternalServerError, bizerr.WrapErr(constant.ErrDB, err))
		return
	}
	if len(items) == 0 {
		c.JSON(http.StatusBadRequest, bizerr.NewBizErrf(constant.ErrParam, "item not found"))
		return
	}

	// 鉴权
	item := items[0]
	queue, err := model.GetUploadQueue(db, item.QueueID.String())
	if err != nil {
		logs.WithError(err).Errorf("fail to get queue %s", item.QueueID.String())
		c.JSON(http.StatusInternalServerError, bizerr.WrapErr(constant.ErrDB, err))
		return
	}
	if queue == nil {
		c.JSON(http.StatusBadRequest, bizerr.NewBizErrf(constant.ErrParam, "item not found"))
		return
	}
	task, err := model.GetCorrectionTask(db, queue.TaskID.String())
	if err != nil {
		logs.WithError(err).Errorf("fail to get task %s", queue.TaskID.String())
		c.JSON(http.StatusInternalServerError, bizerr.WrapErr(constant.ErrDB, err))
		return
	}
	if task == nil {
		c.JSON(http.StatusBadRequest, bizerr.NewBizErrf(constant.ErrParam, "item not found"))
		return
	}
	if task.UserID.String() != token.UserID {
		logs.Infof("item %s not belong to user %s", itemID, token.UserID)
		c.JSON(http.StatusForbidden, gin.H{})
		return
	}

	endpoint := viper.GetString("aliyun.oss.endpoint.public")
	bucketName := viper.GetString("aliyun.oss.bucket.default")
	fileURL, err := signOSSUrl(c, endpoint, bucketName, item.FileID, true, time.Hour, []oss.Option{
		oss.ContentDisposition("inline"),
	})
	if err != nil {
		logs.WithError(err).Errorf("fail to sign oss url %s", item.FileID)
	}

	resp := &UploadQueueItem{
		ItemID:  item.UUID.String(),
		TaskID:  task.UUID.String(),
		FileURL: fileURL,
	}
	if item.AnswerFileID != "" {
		answerFileURL, err := signOSSUrl(c, endpoint, bucketName, item.AnswerFileID, true, time.Hour, []oss.Option{
			oss.ContentDisposition("inline"),
		})
		if err != nil {
			logs.WithError(err).Errorf("fail to sign oss url %s", item.AnswerFileID)
		}
		resp.AnswerFileURL = answerFileURL
	}

	// 添加标记后的图片URL
	if item.MarkedFileID != "" {
		markedFileURL, err := signOSSUrl(c, endpoint, bucketName, item.MarkedFileID, true, time.Hour, []oss.Option{
			oss.ContentDisposition("inline"),
		})
		if err != nil {
			logs.WithError(err).Errorf("fail to sign oss url %s", item.MarkedFileID)
		} else {
			resp.MarkedFileURL = markedFileURL
		}
	}
	c.JSON(http.StatusOK, commonModel.NewSuccessResponse(resp))
}
