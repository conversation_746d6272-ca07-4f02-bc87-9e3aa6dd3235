package controller

import (
	"errors"
	"net/http"

	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/teacher-svc/constant"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/teacher-svc/service"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/teacher-svc/utils"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/model"
	"codeup.aliyun.com/level-up/public/common/bizerr"
	"codeup.aliyun.com/level-up/public/common/clients/gorm"
	"codeup.aliyun.com/level-up/public/common/clients/redis"
	"codeup.aliyun.com/level-up/public/common/logger"
	commonModel "codeup.aliyun.com/level-up/public/common/model"
	commonUtils "codeup.aliyun.com/level-up/public/common/utils"
	"github.com/gin-gonic/gin"
)

type LoginByPhoneAndPasswordRequest struct {
	Phone    string `json:"phone" binding:"required"`
	Password string `json:"password" binding:"required"`
}

type LoginByPhoneAndPasswordResponse struct {
	AccessToken string `json:"accessToken"`
	ExpireTime  int64  `json:"expireTime"`
}

// LoginByPhoneAndPasswordHandler handles user login
func LoginByPhoneAndPasswordHandler(c *gin.Context) {
	logs := logger.GetLogger(c)
	var req LoginByPhoneAndPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrParam, err)))
		return
	}

	db := gorm.GetMultiDB("default")
	user, err := model.GetUserWithPhone(db, req.Phone)
	if err != nil {
		logs.WithError(err).Error("Failed to get user by phone")
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	if user == nil {
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.NewBizErrf(constant.ErrAuth, "Invalid username or password")))
		return
	}

	// Verify password
	isValid, err := service.ValidatePassword(c, user.UUID.String(), req.Password, user.Password)
	if err != nil {
		logs.WithError(err).Error("Failed to validate password")
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}
	if !isValid {
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.NewBizErrf(constant.ErrAuth, "Invalid username or password")))
		return
	}

	// Generate token
	token := utils.GenerateUserToken(c, user.UUID.String())

	// Save token in Redis
	key := redis.GetUserTokenKey(constant.AuthTokenKeyPrefix, token.Token)
	redisClient := redis.GetRedisClient()
	if err := redisClient.SetEx(c, key, user.UUID.String(), utils.MaxUserTokenExpTime).Err(); err != nil {
		logs.WithError(err).Errorf("fail to save key %s", key)
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	response := LoginByPhoneAndPasswordResponse{
		AccessToken: token.Token,
		ExpireTime:  token.ExpiredAt,
	}
	c.JSON(http.StatusOK, commonModel.NewSuccessResponse(response))
}

func LogoutHandler(c *gin.Context) {
	logs := logger.GetLogger(c)
	token := commonUtils.GetCtxAuthToken(c)
	if token == nil {
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.NewBizErrf(constant.ErrAuth, "Unauthorized")))
		return
	}

	key := redis.GetUserTokenKey(constant.AuthTokenKeyPrefix, token.Token)
	redisClient := redis.GetRedisClient()
	if err := redisClient.Del(c, key).Err(); err != nil {
		logs.WithError(err).Errorf("fail to delete key %s", key)
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	c.JSON(http.StatusOK, commonModel.NewSuccessResponse(nil))
}

type SendSMSRequest struct {
	Phone string `json:"phone"`
}

func SendSMS(ginCtx *gin.Context) {
	var req SendSMSRequest
	if err := ginCtx.Bind(&req); err != nil {
		ginCtx.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrParam, err)))
		return
	}

	if _, err := service.SendValidaionCode(ginCtx, req.Phone); err != nil {
		ginCtx.JSON(http.StatusOK, commonModel.NewErrorResponse(err))
		return
	}

	ginCtx.JSON(http.StatusOK, commonModel.NewSuccessResponse(nil))
}

type SMSAuthRequest struct {
	Phone string `json:"phone" binding:"required"`
	Code  string `json:"code" binding:"required"`
}

func SMSAuth(ginCtx *gin.Context) {
	logs := logger.GetLogger(ginCtx)
	var req SMSAuthRequest
	if err := ginCtx.Bind(&req); err != nil {
		ginCtx.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrParam, err)))
		return
	}
	db := gorm.GetMultiDB("")

	if _, err := service.ValidatePhoneCode(ginCtx, req.Phone, req.Code); err != nil {
		ginCtx.JSON(http.StatusOK, commonModel.NewErrorResponse(err))
		return
	}
	// 查询或初始化人员
	user, err := model.GetOrInitUserWithPhone(db, req.Phone, &model.User{})
	if err != nil || user == nil {
		if err == nil {
			err = errors.New("nil user")
		}
		logs.WithError(err).Error("fail to get or init user")
		ginCtx.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	// Generate token
	token := utils.GenerateUserToken(ginCtx, user.UUID.String())

	// Save token in Redis
	key := redis.GetUserTokenKey(constant.AuthTokenKeyPrefix, token.Token)
	redisClient := redis.GetRedisClient()
	if err := redisClient.SetEx(ginCtx, key, user.UUID.String(), utils.MaxUserTokenExpTime).Err(); err != nil {
		logs.WithError(err).Errorf("fail to save key %s", key)
		ginCtx.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	response := LoginByPhoneAndPasswordResponse{
		AccessToken: token.Token,
		ExpireTime:  token.ExpiredAt,
	}
	ginCtx.JSON(http.StatusOK, commonModel.NewSuccessResponse(response))
}
