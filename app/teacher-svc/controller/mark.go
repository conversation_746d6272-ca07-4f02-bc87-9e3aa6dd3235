package controller

import (
	"context"
	"fmt"
	"image"
	"image/color"
	"image/draw"
	"image/jpeg"
	"net/http"
	"os"
	"time"

	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/teacher-svc/constant"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/teacher-svc/service"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/model"
	"codeup.aliyun.com/level-up/public/common/bizerr"
	gormclient "codeup.aliyun.com/level-up/public/common/clients/gorm"
	"codeup.aliyun.com/level-up/public/common/logger"
	commonModel "codeup.aliyun.com/level-up/public/common/model"
	commonUtils "codeup.aliyun.com/level-up/public/common/utils"
	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"github.com/gin-gonic/gin"
	"github.com/rs/xid"
	"github.com/spf13/viper"
	"golang.org/x/image/font"
	"golang.org/x/image/font/basicfont"
	"golang.org/x/image/math/fixed"
	"gorm.io/gorm"
)

// MarkTaskResponse 标记任务响应
type MarkTaskResponse struct {
	MarkedCount int `json:"marked_count"` // 成功标记的数量
}

// MarkSheetResponse 标记试卷响应
type MarkSheetResponse struct {
	MarkedCount int `json:"marked_count"` // 成功标记的数量
}

// MarkItemResponse 标记单个队列项响应
type MarkItemResponse struct {
	MarkedFileURL string `json:"marked_file_url"` // 标记后的图片URL
}

// markItem 为单个队列项生成标记图片
func markItem(ctx context.Context, db *gorm.DB, item *model.UploadQueueItem, correctResult *model.CorrectResult) (string, error) {
	logs := logger.GetLogger(ctx)
	// 获取原始图片URL
	endpoint := viper.GetString("aliyun.oss.endpoint.public")
	bucketName := viper.GetString("aliyun.oss.bucket.default")
	// 使用controller包中的signOSSUrl函数
	fileURL, err := signOSSUrl(ctx, endpoint, bucketName, item.FileID, true, time.Hour, []oss.Option{
		oss.ContentDisposition("inline"),
	})
	if err != nil {
		logs.WithError(err).Errorf("获取原始图片URL失败，fileID: %s", item.FileID)
		return "", err
	}

	// 获取原始图片尺寸
	imgWidth, imgHeight, err := getImageDimensions(ctx, fileURL)
	if err != nil {
		logs.WithError(err).Errorf("获取图片尺寸失败，fileURL: %s", fileURL)
		return "", err
	}

	// 创建空白图片
	var answers []*model.CorrectResultItem
	for idx, answer := range correctResult.Answer {
		if answer.ItemID == item.UUID.String() {
			answers = append(answers, correctResult.Answer[idx])
		}
	}
	markedImagePath, err := createMarkedImage(ctx, imgWidth, imgHeight, answers)
	if err != nil {
		logs.WithError(err).Error("创建标记图片失败")
		return "", err
	}

	// 上传标记图片
	markedFileID := xid.New().String() + ".jpg"
	markedFileID = viper.GetString("aliyun.oss.folder.public") + "/" + markedFileID

	if err := uploadImageToAliyun(ctx, markedImagePath, markedFileID); err != nil {
		logs.WithError(err).Error("上传标记图片失败")
		// 清理临时文件
		os.Remove(markedImagePath)
		return "", err
	}

	// 清理临时文件
	os.Remove(markedImagePath)

	// 更新队列项的MarkedFileID
	if err := model.UpdateUploadQueueItemMarkedFileID(db, item.UUID, markedFileID); err != nil {
		logs.WithError(err).Errorf("更新队列项标记文件ID失败，itemID: %s", item.UUID.String())
		return "", err
	}

	return markedFileID, nil
}

// MarkTask 对批改完成的任务进行原卷留痕
func MarkTask(c *gin.Context) {
	logs := logger.GetLogger(c)

	// 验证用户身份
	token := commonUtils.GetCtxAuthToken(c)
	if token == nil {
		c.JSON(http.StatusUnauthorized, gin.H{})
		return
	}

	// 获取任务ID
	taskID := c.Param("taskID")
	if taskID == "" {
		logs.Error("任务ID不能为空")
		c.JSON(http.StatusBadRequest, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrParam, "任务ID不能为空")))
		return
	}

	// 获取任务
	db := gormclient.GetMultiDB("")
	task, err := model.GetCorrectionTask(db, taskID)
	if err != nil {
		logs.WithError(err).Error("获取批改任务失败")
		c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	if task == nil {
		c.JSON(http.StatusNotFound, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrNotFound, "任务不存在")))
		return
	}

	// 验证任务所有者
	if task.UserID.String() != token.UserID {
		logs.Infof("任务 %s 不属于用户 %s", taskID, token.UserID)
		c.JSON(http.StatusForbidden, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrAuth, "无权访问此任务")))
		return
	}

	// 处理每个答题卡
	markedCount := 0
	sheets, _, err := model.GetSheetsByTaskID(db, taskID, -1, 0, false)
	if err != nil {
		logs.WithError(err).Error("获取试卷列表失败")
		c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}
	for _, sheet := range sheets {
		correctResult := sheet.GetCorrectResult(c, db)
		if correctResult == nil {
			continue
		}
		items, err := model.GetUploadQueueItemsBySheetID(db, sheet.UUID.String())
		if err != nil {
			logs.WithError(err).Error("获取试卷的上传队列项失败")
			continue
		}
		for _, item := range items {
			_, err := markItem(c, db, item, correctResult)
			if err != nil {
				logs.WithError(err).Errorf("标记队列项 %s 失败", item.UUID.String())
				continue
			}
			markedCount++
		}
	}

	// 如果有成功标记的项目，更新任务的MarkedFileChanged标志
	if markedCount > 0 {
		if err := model.UpdateCorrectionTask(db, taskID, map[string]interface{}{
			"marked_file_changed": true,
			"task_marked_file_id": "", // 清空任务留痕文件ID，需要重新生成
		}); err != nil {
			logs.WithError(err).Error("更新任务标记状态失败")
		}
	}

	c.JSON(http.StatusOK, commonModel.NewSuccessResponse(&MarkTaskResponse{
		MarkedCount: markedCount,
	}))
}

// getImageDimensions 获取图片尺寸
func getImageDimensions(ctx context.Context, imageURL string) (int, int, error) {
	logs := logger.GetLogger(ctx)

	// 下载图片
	resp, err := http.Get(imageURL)
	if err != nil {
		logs.WithError(err).Errorf("下载图片失败，URL: %s", imageURL)
		return 0, 0, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		logs.Errorf("下载图片失败，状态码: %d，URL: %s", resp.StatusCode, imageURL)
		return 0, 0, fmt.Errorf("下载图片失败，状态码: %d", resp.StatusCode)
	}

	// 解码图片以获取尺寸
	img, _, err := image.DecodeConfig(resp.Body)
	if err != nil {
		logs.WithError(err).Errorf("解码图片失败，URL: %s", imageURL)
		return 0, 0, err
	}

	return img.Width, img.Height, nil
}

// createMarkedImage 创建标记图片
func createMarkedImage(ctx context.Context, width, height int, answers []*model.CorrectResultItem) (string, error) {
	logs := logger.GetLogger(ctx)

	// 创建空白图片
	img := image.NewRGBA(image.Rect(0, 0, width, height))

	// 填充白色背景
	draw.Draw(img, img.Bounds(), image.White, image.Point{}, draw.Src)

	// 处理每个答案项
	for _, answer := range answers {
		// 跳过没有坐标的答案
		if answer.TopLeft == nil || answer.BottomRight == nil {
			continue
		}

		// 解析坐标
		// x1, _ := answer.TopLeft.X.Float64()
		y1, _ := answer.TopLeft.Y.Float64()
		x2, _ := answer.BottomRight.X.Float64()
		// y2, _ := answer.BottomRight.Y.Float64()

		// 缩放坐标：将坐标除以1000后乘以图片宽高
		// scaledX1 := int(x1 / 1000 * float64(width))
		// scaledY1 := int(y1 / 1000 * float64(height))
		// scaledX2 := int(x2 / 1000 * float64(width))
		// scaledY2 := int(y2 / 1000 * float64(height))
		// scaledX1 := int(x1 * float64(width))
		scaledY1 := int(y1 * float64(height))
		scaledX2 := int(x2 * float64(width))
		// scaledY2 := int(y2 * float64(height))

		// 绘制矩形框
		// drawRectangle(img, scaledX1, scaledY1, scaledX2, scaledY2, color.RGBA{255, 0, 0, 255}, 2)

		// 根据得分或正确率添加标记
		if answer.Score >= 0 {
			// 在框的右上角绘制分数
			scoreText := fmt.Sprintf("%.1f", answer.Score)
			drawText(ctx, img, scaledX2-100, scaledY1+10, scoreText, color.RGBA{255, 0, 0, 255})
		} else {
			// 根据right值绘制符号
			var symbol string
			if answer.Right >= 0.99 {
				symbol = "√" // 正确
			} else if answer.Right <= 0.2 {
				symbol = "X" // 错误
			} else {
				symbol = "X" // 部分正确
			}
			drawText(ctx, img, scaledX2-100, scaledY1+10, symbol, color.RGBA{255, 0, 0, 255})
		}
	}

	// 保存为临时文件
	tempFile, err := os.CreateTemp("", "marked-image-*.jpg")
	if err != nil {
		logs.WithError(err).Error("创建临时文件失败")
		return "", err
	}
	defer tempFile.Close()

	// 编码为JPEG并写入临时文件
	if err := jpeg.Encode(tempFile, img, &jpeg.Options{Quality: 90}); err != nil {
		logs.WithError(err).Error("编码图片失败")
		return "", err
	}

	return tempFile.Name(), nil
}

// drawRectangle 在图像上绘制矩形
func drawRectangle(img *image.RGBA, x1, y1, x2, y2 int, col color.RGBA, thickness int) {
	// 绘制水平线
	for t := 0; t < thickness; t++ {
		// 上边框
		for x := x1; x <= x2; x++ {
			if y1+t >= 0 && y1+t < img.Bounds().Dy() && x >= 0 && x < img.Bounds().Dx() {
				img.Set(x, y1+t, col)
			}
		}
		// 下边框
		for x := x1; x <= x2; x++ {
			if y2-t >= 0 && y2-t < img.Bounds().Dy() && x >= 0 && x < img.Bounds().Dx() {
				img.Set(x, y2-t, col)
			}
		}
	}

	// 绘制垂直线
	for t := 0; t < thickness; t++ {
		// 左边框
		for y := y1; y <= y2; y++ {
			if x1+t >= 0 && x1+t < img.Bounds().Dx() && y >= 0 && y < img.Bounds().Dy() {
				img.Set(x1+t, y, col)
			}
		}
		// 右边框
		for y := y1; y <= y2; y++ {
			if x2-t >= 0 && x2-t < img.Bounds().Dx() && y >= 0 && y < img.Bounds().Dy() {
				img.Set(x2-t, y, col)
			}
		}
	}
}

// drawText 在图像上绘制文本
func drawText(ctx context.Context, img *image.RGBA, x, y int, text string, col color.RGBA) {
	logs := logger.GetLogger(ctx)
	face := service.LoadCorrectFace()
	if face == nil {
		logs.Errorf("fail to get face")
		face = basicfont.Face7x13
	}
	// face = basicfont.Face7x13

	d := &font.Drawer{
		Dst:  img,
		Src:  image.NewUniform(col),
		Face: face,
		Dot: fixed.Point26_6{
			X: fixed.Int26_6(x * 64),
			Y: fixed.Int26_6(y*64) + face.Metrics().Ascent,
		},
	}
	d.DrawString(text)
}

// uploadImageToAliyun 上传图片到aliyun
func uploadImageToAliyun(ctx context.Context, imagePath, fileID string) error {
	logs := logger.GetLogger(ctx)
	innerEndpoint := viper.GetString("aliyun.oss.endpoint.inner")
	bucketName := viper.GetString("aliyun.oss.bucket.default")
	ossClient, err := getOSSClient(innerEndpoint, false)
	if err != nil {
		logs.WithError(err).Errorf("fail to get oss client")
		return bizerr.WrapErr(constant.ErrOSS, err)
	}

	bucket, err := ossClient.Bucket(bucketName)
	if err != nil {
		logs.WithError(err).Errorf("fail to get oss bucket %s", bucketName)
		return bizerr.WrapErr(constant.ErrOSS, err)
	}
	if err := bucket.PutObjectFromFile(fileID, imagePath); err != nil {
		logs.WithError(err).Errorf("fail to upload file")
		return bizerr.WrapErr(constant.ErrOSS, err)
	}

	return nil
}

// MarkSheet 对试卷的所有项目进行原卷留痕
func MarkSheet(c *gin.Context) {
	logs := logger.GetLogger(c)

	// 验证用户身份
	token := commonUtils.GetCtxAuthToken(c)
	if token == nil {
		c.JSON(http.StatusUnauthorized, gin.H{})
		return
	}

	// 获取试卷ID
	sheetID := c.Param("sheetID")
	if sheetID == "" {
		logs.Error("试卷ID不能为空")
		c.JSON(http.StatusBadRequest, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrParam, "试卷ID不能为空")))
		return
	}

	// 获取试卷信息
	db := gormclient.GetMultiDB("")
	sheet, err := model.GetSheetByID(db, sheetID)
	if err != nil {
		logs.WithError(err).Errorf("获取试卷失败，sheetID: %s", sheetID)
		c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	if sheet == nil {
		c.JSON(http.StatusNotFound, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrNotFound, "试卷不存在")))
		return
	}

	// 获取任务信息并鉴权
	task, err := model.GetCorrectionTask(db, sheet.TaskID.String())
	if err != nil {
		logs.WithError(err).Errorf("获取批改任务失败，taskID: %s", sheet.TaskID.String())
		c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	if task == nil {
		c.JSON(http.StatusNotFound, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrNotFound, "任务不存在")))
		return
	}

	// 验证任务所有者
	if task.UserID.String() != token.UserID {
		logs.Infof("试卷 %s 不属于用户 %s", sheetID, token.UserID)
		c.JSON(http.StatusForbidden, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrAuth, "无权访问此试卷")))
		return
	}

	// 获取试卷的所有上传队列项
	items, err := model.GetUploadQueueItemsBySheetID(db, sheet.UUID.String())
	if err != nil {
		logs.WithError(err).Errorf("获取试卷的上传队列项失败，sheetID: %s", sheetID)
		c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}
	correctResult := sheet.GetCorrectResult(c, db)
	if correctResult == nil {
		logs.Errorf("试卷 %s 没有批改结果", sheetID)
		c.JSON(http.StatusBadRequest, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrParam, "试卷没有批改结果")))
		return
	}

	// 处理每个队列项
	markedCount := 0
	for _, item := range items {
		_, err := markItem(c, db, item, correctResult)
		if err != nil {
			logs.WithError(err).Errorf("标记队列项 %s 失败", item.UUID.String())
			continue
		}
		markedCount++
	}

	// 如果有成功标记的项目，更新任务的MarkedFileChanged标志
	if markedCount > 0 {
		if err := model.UpdateCorrectionTask(db, task.UUID.String(), map[string]interface{}{
			"marked_file_changed": true,
			"task_marked_file_id": "", // 清空任务留痕文件ID，需要重新生成
		}); err != nil {
			logs.WithError(err).Error("更新任务标记状态失败")
		}
	}

	c.JSON(http.StatusOK, commonModel.NewSuccessResponse(&MarkSheetResponse{
		MarkedCount: markedCount,
	}))
}

// MarkItem 为单个队列项生成标记图片
func MarkItem(c *gin.Context) {
	logs := logger.GetLogger(c)

	// 验证用户身份
	token := commonUtils.GetCtxAuthToken(c)
	if token == nil {
		c.JSON(http.StatusUnauthorized, gin.H{})
		return
	}
	itemID := c.Params.ByName("itemID")

	// 获取队列项
	db := gormclient.GetMultiDB("")
	var item model.UploadQueueItem
	if err := db.Where("uuid = ?", itemID).First(&item).Error; err != nil {
		logs.WithError(err).Errorf("获取队列项失败，itemID: %s", itemID)
		c.JSON(http.StatusNotFound, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrNotFound, "队列项不存在")))
		return
	}

	// 获取队列
	var queue model.UploadQueue
	if err := db.Where("uuid = ?", item.QueueID).First(&queue).Error; err != nil {
		logs.WithError(err).Errorf("获取队列失败，queueID: %s", item.QueueID.String())
		c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	// 获取任务
	task, err := model.GetCorrectionTask(db, queue.TaskID.String())
	if err != nil {
		logs.WithError(err).Error("获取批改任务失败")
		c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	if task == nil {
		c.JSON(http.StatusNotFound, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrNotFound, "任务不存在")))
		return
	}

	// 验证任务所有者
	if task.UserID.String() != token.UserID {
		logs.Infof("任务 %s 不属于用户 %s", task.UUID.String(), token.UserID)
		c.JSON(http.StatusForbidden, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrAuth, "无权访问此任务")))
		return
	}

	// 获取答题卡信息
	sheet, err := model.GetSheetByItemID(db, itemID)
	if err != nil {
		logs.WithError(err).Errorf("获取试卷失败，itemID: %s", itemID)
		c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}
	if sheet == nil {
		c.JSON(http.StatusNotFound, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrNotFound, "试卷不存在")))
		return
	}
	correctResult := sheet.GetCorrectResult(c, db)
	if correctResult == nil {
		c.JSON(http.StatusBadRequest, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrParam, "试卷没有批改结果")))
		return
	}

	// 标记队列项
	markedFileID, err := markItem(c, db, &item, correctResult)
	if err != nil {
		logs.WithError(err).Errorf("标记队列项失败，itemID: %s", itemID)
		c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrServer, err)))
		return
	}

	// 更新任务的MarkedFileChanged标志
	if err := model.UpdateCorrectionTask(db, task.UUID.String(), map[string]interface{}{
		"marked_file_changed": true,
		"task_marked_file_id": "", // 清空任务留痕文件ID，需要重新生成
	}); err != nil {
		logs.WithError(err).Error("更新任务标记状态失败")
		// 继续返回URL，不中断流程
	}

	// 获取标记图片URL
	endpoint := viper.GetString("aliyun.oss.endpoint.public")
	bucketName := viper.GetString("aliyun.oss.bucket.default")
	markedFileURL, err := signOSSUrl(c, endpoint, bucketName, markedFileID, true, time.Hour, []oss.Option{
		oss.ContentDisposition("inline"),
	})
	if err != nil {
		logs.WithError(err).Errorf("获取标记图片URL失败，fileID: %s", markedFileID)
		c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrServer, err)))
		return
	}

	c.JSON(http.StatusOK, commonModel.NewSuccessResponse(&MarkItemResponse{
		MarkedFileURL: markedFileURL,
	}))
}
