package controller

import (
	"net/http"

	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/teacher-svc/constant"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/teacher-svc/service"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/model"
	"codeup.aliyun.com/level-up/public/common/bizerr"
	gormClient "codeup.aliyun.com/level-up/public/common/clients/gorm"
	"codeup.aliyun.com/level-up/public/common/logger"
	commonModel "codeup.aliyun.com/level-up/public/common/model"
	commonUtils "codeup.aliyun.com/level-up/public/common/utils"
	"github.com/gin-gonic/gin"
)

type JoinTenantRequest struct {
	TenantCode string `json:"tenantCode" binding:"required"`
}

func JoinTenant(c *gin.Context) {
	logs := logger.GetLogger(c)
	token := commonUtils.GetCtxAuthToken(c)
	if token == nil {
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.NewBizErrf(constant.ErrAuth, "Unauthorized")))
		return
	}

	var req JoinTenantRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logs.WithError(err).Error("Failed to bind uri parameters")
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrParam, err)))
		return
	}

	db := gormClient.GetMultiDB("default")
	tx := db.Begin()
	if err := tx.Error; err != nil {
		logs.WithError(err).Error("Failed to begin transaction")
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	err := service.JoinTenant(tx, token.UserID, req.TenantCode)
	if err != nil {
		tx.Rollback()
		logs.WithError(err).Error("Failed to join tenant")
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	if err := tx.Commit().Error; err != nil {
		logs.WithError(err).Error("Failed to commit transaction")
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	c.JSON(http.StatusOK, commonModel.NewSuccessResponse(nil))
}

type GetJoinTenantStatusRequest struct {
	TenantID string `form:"tenantId" binding:"required"`
}

type GetJoinTenantStatusResponse struct {
	Status string `json:"status"`
}

func GetJoinTenantStatus(c *gin.Context) {
	logs := logger.GetLogger(c)
	token := commonUtils.GetCtxAuthToken(c)
	if token == nil {
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.NewBizErrf(constant.ErrAuth, "Unauthorized")))
		return
	}

	var req GetJoinTenantStatusRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		logs.WithError(err).Error("Failed to bind query parameters")
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrParam, err)))
		return
	}

	db := gormClient.GetMultiDB("default")
	tenantUser, err := model.GetTenantUserByTenantAndUserID(db, req.TenantID, token.UserID)
	if err != nil {
		logs.WithError(err).Error("Failed to get join tenant status")
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	response := GetJoinTenantStatusResponse{
		Status: string(tenantUser.Status),
	}
	c.JSON(http.StatusOK, commonModel.NewSuccessResponse(response))
}

type GetTenantDetailRequest struct {
	TenantID string `form:"tenantId" binding:"required"`
}

type GetTenantDetailResponse struct {
	TenantID    string `json:"tenantId"`
	TenantName  string `json:"tenantName"`
	TenantPoint int64  `json:"tenantPoint"`
}

func GetTenantDetail(c *gin.Context) {
	logs := logger.GetLogger(c)
	token := commonUtils.GetCtxAuthToken(c)
	if token == nil {
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.NewBizErrf(constant.ErrAuth, "Unauthorized")))
		return
	}

	var req GetTenantDetailRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		logs.WithError(err).Error("Failed to bind uri parameters")
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrParam, err)))
		return
	}
	if req.TenantID == "" {
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.NewBizErrf(constant.ErrParam, "Tenant ID is required")))
		return
	}

	db := gormClient.GetMultiDB("default")
	// check if user is admin
	isTenantUser, err := service.CheckTenantUserRole(db, req.TenantID, token.UserID)
	if err != nil {
		logs.WithError(err).Error("Failed to check tenant user role")
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	if !isTenantUser {
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.NewBizErrf(constant.ErrAuth, "Unauthorized")))
		return
	}

	tenant, err := model.GetTenantDetailByID(db, req.TenantID)
	if err != nil {
		logs.WithError(err).Error("Failed to get tenant details")
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	if tenant == nil {
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.NewBizErrf(constant.ErrAuth, "Tenant not found")))
		return
	}

	response := GetTenantDetailResponse{
		TenantID:    tenant.TenantID,
		TenantName:  tenant.TenantName,
		TenantPoint: tenant.TenantPoint,
	}

	c.JSON(http.StatusOK, commonModel.NewSuccessResponse(response))
}

type GetTenantsResponse struct {
	TenantID   string `json:"tenantId"`
	TenantName string `json:"tenantName"`
}

func GetTenants(c *gin.Context) {
	logs := logger.GetLogger(c)
	token := commonUtils.GetCtxAuthToken(c)
	if token == nil {
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.NewBizErrf(constant.ErrAuth, "Unauthorized")))
		return
	}

	db := gormClient.GetMultiDB("default")
	tenants, _, err := model.GetTenants(db, 0, 10)
	if err != nil {
		logs.WithError(err).Error("Failed to get tenants")
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	var response []GetTenantsResponse
	for _, tenant := range tenants {
		response = append(response, GetTenantsResponse{
			TenantID:   tenant.UUID.String(),
			TenantName: tenant.Name,
		})
	}

	c.JSON(http.StatusOK, commonModel.NewSuccessResponse(response))
}
