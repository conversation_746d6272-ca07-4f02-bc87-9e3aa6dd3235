package controller

import (
	"net/http"

	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/teacher-svc/constant"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/teacher-svc/middleware"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/teacher-svc/service"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/model"
	"codeup.aliyun.com/level-up/public/common/bizerr"
	gormclient "codeup.aliyun.com/level-up/public/common/clients/gorm"
	"codeup.aliyun.com/level-up/public/common/logger"
	commonModel "codeup.aliyun.com/level-up/public/common/model"
	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
)

// CreateUploadQueueRequest 创建上传队列请求
type CreateUploadQueueRequest struct {
	Total int `json:"total"` // 总图片数量，可选
}

// CreateUploadQueueResponse 创建上传队列响应
type CreateUploadQueueResponse struct {
	QueueID string `json:"queue_id"` // 上传队列ID
}

// SubmitQueueItemRequest 提交队列项请求
type SubmitQueueItemRequest struct {
	QueueID  string `json:"queue_id" binding:"required"` // 上传队列ID
	FileID   string `json:"file_id" binding:"required"`  // 文件ID
	Sequence int    `json:"sequence" binding:"required"` // 序号
}

// CreateUploadQueue 创建上传队列
func CreateUploadQueue(c *gin.Context) {
	logs := logger.GetLogger(c)

	auth := middleware.GetPrinterAuth(c)
	if auth == nil || auth.TaskID == uuid.Nil {
		c.JSON(http.StatusUnauthorized, gin.H{})
		return
	}
	taskID := auth.TaskID.String()

	// 解析请求参数
	var req CreateUploadQueueRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logs.WithError(err).Error("解析创建上传队列参数失败")
		c.JSON(http.StatusBadRequest, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrParam, err)))
		return
	}

	// 获取任务
	db := gormclient.GetMultiDB("")
	task, err := model.GetCorrectionTask(db, taskID)
	if err != nil {
		logs.WithError(err).Error("获取批改任务失败")
		c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	if task == nil {
		c.JSON(http.StatusNotFound, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrNotFound, "任务不存在")))
		return
	}

	// 创建上传队列
	queue := &model.UploadQueue{
		UUID:   uuid.NewV4(),
		TaskID: auth.TaskID,
	}

	if err := model.CreateUploadQueue(db, queue); err != nil {
		logs.WithError(err).Error("创建上传队列失败")
		c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	c.JSON(http.StatusOK, commonModel.NewSuccessResponse(&CreateUploadQueueResponse{
		QueueID: queue.UUID.String(),
	}))
}

// SubmitQueueItem 提交队列项
func SubmitQueueItem(c *gin.Context) {
	logs := logger.GetLogger(c)

	auth := middleware.GetPrinterAuth(c)
	if auth == nil || auth.TaskID == uuid.Nil {
		c.JSON(http.StatusUnauthorized, gin.H{})
		return
	}
	taskID := auth.TaskID.String()

	// 解析请求参数
	var req SubmitQueueItemRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logs.WithError(err).Error("解析提交队列项参数失败")
		c.JSON(http.StatusBadRequest, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrParam, err)))
		return
	}

	// 获取上传队列
	db := gormclient.GetMultiDB("")
	queue, err := model.GetUploadQueue(db, req.QueueID)
	if err != nil {
		logs.WithError(err).Error("获取上传队列失败")
		c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}
	if queue == nil {
		c.JSON(http.StatusNotFound, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrNotFound, "上传队列不存在")))
		return
	}
	if queue.TaskID.String() != taskID {
		c.JSON(http.StatusForbidden, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrAuth, "无权访问此上传队列")))
		return
	}

	// 创建上传队列项
	item := &model.UploadQueueItem{
		UUID:     uuid.NewV4(),
		QueueID:  uuid.FromStringOrNil(req.QueueID),
		FileID:   req.FileID,
		Sequence: req.Sequence,
	}

	if err := model.CreateUploadQueueItem(db, item); err != nil {
		logs.WithError(err).Error("创建上传队列项失败")
		c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}
	// 触发批改
	if err := service.AsyncCorrectSheetOrItem(c, db, item.UUID.String(), ""); err != nil {
		logs.WithError(err).Error("触发批改失败")
	}

	// 更新任务的MarkedFileChanged标志
	if err := model.UpdateCorrectionTask(db, taskID, map[string]interface{}{
		"marked_file_changed": true,
	}); err != nil {
		logs.WithError(err).Error("更新任务标记状态失败")
		// 不中断流程
	}

	c.JSON(http.StatusOK, commonModel.NewSuccessResponse(gin.H{}))
}
