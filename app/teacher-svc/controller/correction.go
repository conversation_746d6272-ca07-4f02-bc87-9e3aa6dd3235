package controller

import (
	"context"
	"net/http"
	"time"

	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/teacher-svc/constant"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/teacher-svc/middleware"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/model"
	"codeup.aliyun.com/level-up/public/common/bizerr"
	gormclient "codeup.aliyun.com/level-up/public/common/clients/gorm"
	"codeup.aliyun.com/level-up/public/common/logger"
	commonModel "codeup.aliyun.com/level-up/public/common/model"
	"codeup.aliyun.com/level-up/public/common/utils"
	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
	"github.com/spf13/viper"
	"gorm.io/gorm"
)

const (
	DefaultLimit = 20
	MaxLimit     = 100
)

type ListCorrectionTaskRequest struct {
	Page int `form:"page"`
	Size int `form:"size"`
}

// CreateCorrectionTaskRequest 创建批改任务请求
type CreateCorrectionTaskRequest struct {
	Title                     string `json:"title" binding:"required"`    // 任务标题
	NeedScanPaper             bool   `json:"need_scan_paper"`             // 是否需要扫描试卷题目
	PaperWithAnswer           bool   `json:"paper_with_answer"`           // 答题卡和试卷题目是否在一起
	AutoCorrection            bool   `json:"auto_correction"`             // 是否自动批改
	SheetsPerPaper            int    `json:"sheets_per_paper"`            // 每张答题卡有多少张纸
	DoubleSided               bool   `json:"double_sided"`                // 是否双面打印(通用)
	VerticalArrangement       bool   `json:"vertical_arrangement"`        // 是否竖向放入(通用)
	PaperDoubleSided          bool   `json:"paper_double_sided"`          // 试卷是否双面打印
	PaperVerticalArrangement  bool   `json:"paper_vertical_arrangement"`  // 试卷是否竖向放入
	AnswerDoubleSided         bool   `json:"answer_double_sided"`         // 答案是否双面打印
	AnswerVerticalArrangement bool   `json:"answer_vertical_arrangement"` // 答案是否竖向放入
}

// TaskListResponse 任务列表响应
type TaskListResponse struct {
	Total int64                `json:"total"` // 总数
	List  []*CorrectionTaskDTO `json:"list"`  // 任务列表
}

// CorrectionTaskDTO 批改任务展示对象
type CorrectionTaskDTO struct {
	ID                        string    `json:"id"`                          // 任务ID
	Title                     string    `json:"title"`                       // 任务标题
	Status                    string    `json:"status"`                      // 任务状态
	NeedScanPaper             bool      `json:"need_scan_paper"`             // 是否需要扫描试卷题目
	PaperWithAnswer           bool      `json:"paper_with_answer"`           // 答题卡和试卷题目是否在一起
	SheetsPerPaper            int       `json:"sheets_per_paper"`            // 每张答题卡有多少张纸
	DoubleSided               bool      `json:"double_sided"`                // 是否双面打印(通用)
	VerticalArrangement       bool      `json:"vertical_arrangement"`        // 是否竖向放入(通用)
	PaperDoubleSided          bool      `json:"paper_double_sided"`          // 试卷是否双面打印
	PaperVerticalArrangement  bool      `json:"paper_vertical_arrangement"`  // 试卷是否竖向放入
	AnswerDoubleSided         bool      `json:"answer_double_sided"`         // 答案是否双面打印
	AnswerVerticalArrangement bool      `json:"answer_vertical_arrangement"` // 答案是否竖向放入
	PaperFileURLs             []string  `json:"paper_file_urls"`             // 试卷文件URL列表
	AnswerFileURLs            []string  `json:"answer_file_urls"`            // 答案文件URL列表
	PaperParseStatus          string    `json:"paper_parse_status"`          // 试卷解析状态
	AnswerParseStatus         string    `json:"answer_parse_status"`         // 答案解析状态
	TaskMarkedFileURL         string    `json:"task_marked_file_url"`        // 整个任务留痕文件URL
	MarkedFileChanged         bool      `json:"marked_file_changed"`         // 留痕结果是否改变
	TotalSheets               int       `json:"total_sheets"`                // 总答题卡数量
	CorrectedSheets           int       `json:"corrected_sheets"`            // 已批改答题卡数量
	InProgressSheets          int       `json:"in_progress_sheets"`          // 批改中的答题卡数量
	FailedSheets              int       `json:"failed_sheets"`               // 批改失败的答题卡数量
	CreatedAt                 time.Time `json:"created_at"`                  // 创建时间
	UpdatedAt                 time.Time `json:"updated_at"`                  // 更新时间
}

// SheetListResponse 答题卡列表响应
type SheetListResponse struct {
	Total int64                 `json:"total"` // 总数
	List  []*CorrectionSheetDTO `json:"list"`  // 答题卡列表
}

// CorrectionSheetDTO 答题卡批改展示对象
type CorrectionSheetDTO struct {
	ID          string     `json:"id"`           // 答题卡ID
	TaskID      string     `json:"task_id"`      // 任务ID
	StudentName string     `json:"student_name"` // 学生姓名
	StudentID   string     `json:"student_id"`   // 学生ID
	Score       float64    `json:"score"`        // 得分
	TotalScore  float64    `json:"total_score"`  // 总分
	Status      string     `json:"status"`       // 状态
	CorrectedAt *time.Time `json:"corrected_at"` // 批改完成时间
}

// UpdatePaperRequest 更新试卷请求
type UpdatePaperRequest struct {
	PaperFileIDs  []string `json:"paper_file_ids,omitempty"`  // 试卷文件ID列表
	AnswerFileIDs []string `json:"answer_file_ids,omitempty"` // 答案文件ID列表
}

// SubmitAnswerSheetRequest 提交答题卡请求
type SubmitAnswerSheetRequest struct {
	StudentName  string   `json:"student_name" binding:"required"`   // 学生姓名
	StudentID    string   `json:"student_id" binding:"required"`     // 学生ID
	SheetFileIDs []string `json:"sheet_file_ids" binding:"required"` // 答题卡文件ID列表
}

// UpdateTaskSettingsRequest 更新任务设置请求
type UpdateTaskSettingsRequest struct {
	NeedScanPaper             *bool   `json:"need_scan_paper,omitempty"`             // 是否需要扫描试卷题目
	PaperWithAnswer           *bool   `json:"paper_with_answer,omitempty"`           // 答题卡和试卷题目是否在一起
	AutoCorrection            *bool   `json:"auto_correction,omitempty"`             // 是否自动批改
	SheetsPerPaper            *int    `json:"sheets_per_paper,omitempty"`            // 每张答题卡有多少张纸
	DoubleSided               *bool   `json:"double_sided,omitempty"`                // 是否双面打印(通用)
	VerticalArrangement       *bool   `json:"vertical_arrangement,omitempty"`        // 是否竖向放入(通用)
	PaperDoubleSided          *bool   `json:"paper_double_sided,omitempty"`          // 试卷是否双面打印
	PaperVerticalArrangement  *bool   `json:"paper_vertical_arrangement,omitempty"`  // 试卷是否竖向放入
	AnswerDoubleSided         *bool   `json:"answer_double_sided,omitempty"`         // 答案是否双面打印
	AnswerVerticalArrangement *bool   `json:"answer_vertical_arrangement,omitempty"` // 答案是否竖向放入
	Title                     *string `json:"title,omitempty"`                       // 任务标题
}

// GetSignedFileURLs 获取已签名的文件URL
func GetSignedFileURLs(ctx context.Context, fileIDs []string) ([]string, error) {
	logs := logger.GetLogger(ctx)
	result := make([]string, 0, len(fileIDs))

	for _, fileID := range fileIDs {
		if fileID == "" {
			continue
		}

		// 获取文件访问链接
		endpoint := viper.GetString("aliyun.oss.endpoint.public")
		bucketName := viper.GetString("aliyun.oss.bucket.default")
		fileURL, err := signOSSUrl(ctx, endpoint, bucketName, fileID, true, time.Hour, []oss.Option{
			oss.ContentDisposition("inline"),
		})
		if err != nil {
			logs.WithError(err).Errorf("fail to sign oss url %s", fileID)
			continue
		}

		result = append(result, fileURL)
	}

	return result, nil
}

// calculateTaskStatistics 计算任务的统计数据
func calculateTaskStatistics(db *gorm.DB, taskID string) (totalItems int, totalCorrected int, inProgressItems int, failedItems int, err error) {
	var total int64

	// 查询总数
	query := db.Table("upload_queue_items").
		Joins("LEFT JOIN upload_queues ON upload_queue_items.queue_id = upload_queues.uuid").
		Where("upload_queues.task_id = ?", taskID)

	if err := query.Count(&total).Error; err != nil {
		return 0, 0, 0, 0, err
	}

	totalItems = int(total)

	// 查询已扫描的数量（所有状态不为空的项目）
	var scannedCount int64
	if err := query.Where("upload_queue_items.status != ''").Count(&scannedCount).Error; err != nil {
		return totalItems, 0, 0, 0, err
	}

	// 查询已批改的数量（状态为completed的项目）
	var correctedCount int64
	if err := query.Where("upload_queue_items.status = ?", model.CorrectionSheetStatusCompleted).Count(&correctedCount).Error; err != nil {
		return totalItems, 0, 0, 0, err
	}

	totalCorrected = int(correctedCount)

	// 查询批改中的数量（状态为in_progress的项目）
	var inProgressCount int64
	if err := query.Where("upload_queue_items.status = ?", model.CorrectionSheetStatusInProgress).Count(&inProgressCount).Error; err != nil {
		return totalItems, totalCorrected, 0, 0, err
	}

	inProgressItems = int(inProgressCount)

	// 查询批改失败的数量（状态为failed的项目）
	var failedCount int64
	if err := query.Where("upload_queue_items.status = ?", model.CorrectionSheetStatusFailed).Count(&failedCount).Error; err != nil {
		return totalItems, totalCorrected, inProgressItems, 0, err
	}

	failedItems = int(failedCount)

	return totalItems, totalCorrected, inProgressItems, failedItems, nil
}

// CreateCorrectionTask 创建批改任务
func CreateCorrectionTask(c *gin.Context) {
	logs := logger.GetLogger(c)

	// 验证用户身份
	token := utils.GetCtxAuthToken(c)
	if token == nil {
		c.JSON(http.StatusUnauthorized, gin.H{})
		return
	}

	// 解析请求参数
	var req CreateCorrectionTaskRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logs.WithError(err).Error("解析创建批改任务参数失败")
		c.JSON(http.StatusBadRequest, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrParam, err)))
		return
	}

	// 设置SheetsPerPaper的默认值为1（如果客户端传入0或负数）
	if req.SheetsPerPaper <= 0 {
		req.SheetsPerPaper = 1
	}

	// 创建任务
	db := gormclient.GetMultiDB("")

	// 根据配置决定初始状态
	initialStatus := model.CorrectionTaskStatusWaitingForPaper
	if !req.NeedScanPaper {
		initialStatus = model.CorrectionTaskStatusWaitingForAnswer
	}

	task := &model.CorrectionTask{
		UUID:                      uuid.NewV4(),
		UserID:                    uuid.FromStringOrNil(token.UserID),
		Title:                     req.Title,
		Status:                    initialStatus,
		NeedScanPaper:             req.NeedScanPaper,
		PaperWithAnswer:           req.PaperWithAnswer,
		SheetsPerPaper:            req.SheetsPerPaper,
		DoubleSided:               req.DoubleSided,
		VerticalArrangement:       req.VerticalArrangement,
		PaperDoubleSided:          req.PaperDoubleSided,
		PaperVerticalArrangement:  req.PaperVerticalArrangement,
		AnswerDoubleSided:         req.AnswerDoubleSided,
		AnswerVerticalArrangement: req.AnswerVerticalArrangement,
	}

	if err := model.CreateCorrectionTask(db, task); err != nil {
		logs.WithError(err).Error("创建批改任务失败")
		c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	c.JSON(http.StatusOK, commonModel.NewSuccessResponse(gin.H{
		"id": task.UUID.String(),
	}))
}

// GetCorrectionTask 获取批改任务详情
func GetCorrectionTask(printerAuth bool) gin.HandlerFunc {
	return func(c *gin.Context) {
		logs := logger.GetLogger(c)

		// 验证用户身份
		token := utils.GetCtxAuthToken(c)
		if !printerAuth && token == nil {
			c.JSON(http.StatusUnauthorized, gin.H{})
			return
		}

		// 获取任务ID
		taskID := c.Param("taskID")
		if !printerAuth && taskID == "" {
			c.JSON(http.StatusBadRequest, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrParam, "任务ID不能为空")))
			return
		}

		if printerAuth {
			auth := middleware.GetPrinterAuth(c)
			if auth == nil || auth.TaskID == uuid.Nil {
				c.JSON(http.StatusUnauthorized, gin.H{})
				return
			}
			taskID = auth.TaskID.String()
		}

		// 获取任务
		db := gormclient.GetMultiDB("")
		task, err := model.GetCorrectionTask(db, taskID)
		if err != nil {
			logs.WithError(err).Error("获取批改任务失败")
			c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrDB, err)))
			return
		}

		if task == nil {
			c.JSON(http.StatusNotFound, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrNotFound, "任务不存在")))
			return
		}

		// 检查任务是否属于当前用户
		if !printerAuth && task.UserID.String() != token.UserID {
			c.JSON(http.StatusForbidden, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrAuth, "无权访问此任务")))
			return
		}

		// 获取签名后的文件URL
		paperFileIDs := task.GetPaperFileIDsArray()
		answerFileIDs := task.GetAnswerFileIDsArray()

		// 获取签名后的文件URL
		paperFileURLs, err := GetSignedFileURLs(c, paperFileIDs)
		if err != nil {
			logs.WithError(err).Error("获取试卷文件URL失败")
		}

		answerFileURLs, err := GetSignedFileURLs(c, answerFileIDs)
		if err != nil {
			logs.WithError(err).Error("获取答案文件URL失败")
		}

		// 获取任务留痕文件URL
		var taskMarkedFileURL string
		if task.TaskMarkedFileID != "" {
			taskMarkedFileURLs, err := GetSignedFileURLs(c, []string{task.TaskMarkedFileID})
			if err != nil {
				logs.WithError(err).Error("获取任务留痕文件URL失败")
			} else if len(taskMarkedFileURLs) > 0 {
				taskMarkedFileURL = taskMarkedFileURLs[0]
			}
		}

		// 实时计算统计数据
		totalItems, totalCorrected, inProgressItems, failedItems, err := calculateTaskStatistics(db, task.UUID.String())
		if err != nil {
			logs.WithError(err).Error("计算任务统计数据失败")
		}

		// 返回任务详情
		c.JSON(http.StatusOK, commonModel.NewSuccessResponse(&CorrectionTaskDTO{
			ID:                        task.UUID.String(),
			Title:                     task.Title,
			Status:                    task.GetActualStatus(),
			NeedScanPaper:             task.NeedScanPaper,
			PaperWithAnswer:           task.PaperWithAnswer,
			SheetsPerPaper:            task.SheetsPerPaper,
			DoubleSided:               task.DoubleSided,
			VerticalArrangement:       task.VerticalArrangement,
			PaperDoubleSided:          task.PaperDoubleSided,
			PaperVerticalArrangement:  task.PaperVerticalArrangement,
			AnswerDoubleSided:         task.AnswerDoubleSided,
			AnswerVerticalArrangement: task.AnswerVerticalArrangement,
			PaperFileURLs:             paperFileURLs,
			AnswerFileURLs:            answerFileURLs,
			PaperParseStatus:          task.PaperParseStatus,
			AnswerParseStatus:         task.AnswerParseStatus,
			TaskMarkedFileURL:         taskMarkedFileURL,
			MarkedFileChanged:         task.MarkedFileChanged,
			TotalSheets:               totalItems,
			CorrectedSheets:           totalCorrected,
			InProgressSheets:          inProgressItems,
			FailedSheets:              failedItems,
			CreatedAt:                 task.CreatedAt,
			UpdatedAt:                 task.UpdatedAt,
		}))
	}
}

// ListCorrectionTasks 获取批改任务列表
func ListCorrectionTasks(c *gin.Context) {
	logs := logger.GetLogger(c)

	// 验证用户身份
	token := utils.GetCtxAuthToken(c)
	if token == nil {
		c.JSON(http.StatusUnauthorized, gin.H{})
		return
	}

	// 获取分页参数
	var req ListCorrectionTaskRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		logs.WithError(err).Error("解析分页参数失败")
		c.JSON(http.StatusBadRequest, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrParam, err)))
		return
	}

	offset, limit := utils.GetLimitOffset(req.Page, req.Size)

	// 获取任务列表
	db := gormclient.GetMultiDB("")
	tasks, err := model.ListCorrectionTasks(db, token.UserID, limit, offset)
	if err != nil {
		logs.WithError(err).Error("获取批改任务列表失败")
		c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	// 统计总数
	total, err := model.CountCorrectionTasks(db, token.UserID)
	if err != nil {
		logs.WithError(err).Error("统计批改任务总数失败")
		c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	// 转换为DTO
	taskDTOs := make([]*CorrectionTaskDTO, 0, len(tasks))
	for _, task := range tasks {
		// 获取签名后的文件URL
		paperFileIDs := task.GetPaperFileIDsArray()
		answerFileIDs := task.GetAnswerFileIDsArray()

		// 获取签名后的文件URL
		paperFileURLs, err := GetSignedFileURLs(c, paperFileIDs)
		if err != nil {
			logs.WithError(err).Error("获取试卷文件URL失败")
		}

		answerFileURLs, err := GetSignedFileURLs(c, answerFileIDs)
		if err != nil {
			logs.WithError(err).Error("获取答案文件URL失败")
		}

		// 获取任务留痕文件URL
		var taskMarkedFileURL string
		if task.TaskMarkedFileID != "" {
			taskMarkedFileURLs, err := GetSignedFileURLs(c, []string{task.TaskMarkedFileID})
			if err != nil {
				logs.WithError(err).Error("获取任务留痕文件URL失败")
			} else if len(taskMarkedFileURLs) > 0 {
				taskMarkedFileURL = taskMarkedFileURLs[0]
			}
		}

		// 实时计算统计数据
		totalItems, totalCorrected, inProgressItems, failedItems, err := calculateTaskStatistics(db, task.UUID.String())
		if err != nil {
			logs.WithError(err).Error("计算任务统计数据失败")
		}

		taskDTOs = append(taskDTOs, &CorrectionTaskDTO{
			ID:                        task.UUID.String(),
			Title:                     task.Title,
			Status:                    task.GetActualStatus(),
			NeedScanPaper:             task.NeedScanPaper,
			PaperWithAnswer:           task.PaperWithAnswer,
			SheetsPerPaper:            task.SheetsPerPaper,
			DoubleSided:               task.DoubleSided,
			VerticalArrangement:       task.VerticalArrangement,
			PaperDoubleSided:          task.PaperDoubleSided,
			PaperVerticalArrangement:  task.PaperVerticalArrangement,
			AnswerDoubleSided:         task.AnswerDoubleSided,
			AnswerVerticalArrangement: task.AnswerVerticalArrangement,
			PaperFileURLs:             paperFileURLs,
			AnswerFileURLs:            answerFileURLs,
			PaperParseStatus:          task.PaperParseStatus,
			AnswerParseStatus:         task.AnswerParseStatus,
			TaskMarkedFileURL:         taskMarkedFileURL,
			MarkedFileChanged:         task.MarkedFileChanged,
			TotalSheets:               totalItems,
			CorrectedSheets:           totalCorrected,
			InProgressSheets:          inProgressItems,
			FailedSheets:              failedItems,
			CreatedAt:                 task.CreatedAt,
			UpdatedAt:                 task.UpdatedAt,
		})
	}

	c.JSON(http.StatusOK, commonModel.NewSuccessListResponse(req.Page, req.Size, total, taskDTOs))
}

// UpdatePaper 更新试卷和答案
func UpdatePaper(c *gin.Context) {
	logs := logger.GetLogger(c)

	// 从打印机授权中获取任务ID
	auth := middleware.GetPrinterAuth(c)
	taskID := auth.TaskID.String()

	// 解析请求参数
	var req UpdatePaperRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logs.WithError(err).Error("解析更新试卷参数失败")
		c.JSON(http.StatusBadRequest, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrParam, err)))
		return
	}

	// 获取任务
	db := gormclient.GetMultiDB("")
	task, err := model.GetCorrectionTask(db, taskID)
	if err != nil {
		logs.WithError(err).Error("获取批改任务失败")
		c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	if task == nil {
		c.JSON(http.StatusNotFound, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrNotFound, "任务不存在")))
		return
	}

	// 更新试卷文件ID和答案文件ID
	updateMap := make(map[string]interface{})
	if len(req.PaperFileIDs) > 0 {
		task.SetPaperFileIDsArray(req.PaperFileIDs)
		updateMap["paper_file_ids"] = task.PaperFileIDs

		// 更新原始状态
		if task.GetActualStatus() == model.CorrectionTaskStatusWaitingForPaper {
			// 如果答题卡和试卷在一起，更新为scanning状态
			if task.PaperWithAnswer {
				updateMap["status"] = model.CorrectionTaskStatusScanning
			} else {
				// 否则更新为等待扫描答案
				updateMap["status"] = model.CorrectionTaskStatusWaitingForAnswer
			}
		}
	}

	if len(req.AnswerFileIDs) > 0 {
		task.SetAnswerFileIDsArray(req.AnswerFileIDs)
		updateMap["answer_file_ids"] = task.AnswerFileIDs

		// 如果当前状态是等待扫描答案，则更新为扫描答题卡中
		if task.GetActualStatus() == model.CorrectionTaskStatusWaitingForAnswer {
			updateMap["status"] = model.CorrectionTaskStatusScanning
		}
	}

	// 更新任务
	if len(updateMap) > 0 {
		if err := model.UpdateCorrectionTask(db, taskID, updateMap); err != nil {
			logs.WithError(err).Error("更新批改任务失败")
			c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrDB, err)))
			return
		}
	}

	c.JSON(http.StatusOK, commonModel.NewSuccessResponse(gin.H{}))
}

// UpdateTaskSettings 更新任务设置
func UpdateTaskSettings(c *gin.Context) {
	logs := logger.GetLogger(c)

	// 验证用户身份
	token := utils.GetCtxAuthToken(c)
	if token == nil {
		c.JSON(http.StatusUnauthorized, gin.H{})
		return
	}

	// 获取任务ID
	taskID := c.Param("taskID")
	if taskID == "" {
		c.JSON(http.StatusBadRequest, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrParam, "任务ID不能为空")))
		return
	}

	// 解析请求参数
	var req UpdateTaskSettingsRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logs.WithError(err).Error("解析更新任务设置参数失败")
		c.JSON(http.StatusBadRequest, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrParam, err)))
		return
	}

	// 获取任务
	db := gormclient.GetMultiDB("")
	task, err := model.GetCorrectionTask(db, taskID)
	if err != nil {
		logs.WithError(err).Error("获取批改任务失败")
		c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	if task == nil {
		c.JSON(http.StatusNotFound, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrNotFound, "任务不存在")))
		return
	}

	// 检查任务是否属于当前用户
	if task.UserID.String() != token.UserID {
		c.JSON(http.StatusForbidden, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrAuth, "无权访问此任务")))
		return
	}

	// 更新任务设置
	updateMap := make(map[string]interface{})
	configChanged := false

	if req.NeedScanPaper != nil {
		updateMap["need_scan_paper"] = *req.NeedScanPaper
		configChanged = true
	}
	if req.PaperWithAnswer != nil {
		updateMap["paper_with_answer"] = *req.PaperWithAnswer
		configChanged = true
	}
	if req.AutoCorrection != nil {
		updateMap["auto_correction"] = *req.AutoCorrection
	}
	if req.SheetsPerPaper != nil {
		updateMap["sheets_per_paper"] = *req.SheetsPerPaper
	}
	if req.DoubleSided != nil {
		updateMap["double_sided"] = *req.DoubleSided
	}
	if req.VerticalArrangement != nil {
		updateMap["vertical_arrangement"] = *req.VerticalArrangement
	}
	if req.Title != nil && *req.Title != "" {
		updateMap["title"] = *req.Title
	}
	if req.PaperDoubleSided != nil {
		updateMap["paper_double_sided"] = *req.PaperDoubleSided
	}
	if req.PaperVerticalArrangement != nil {
		updateMap["paper_vertical_arrangement"] = *req.PaperVerticalArrangement
	}
	if req.AnswerDoubleSided != nil {
		updateMap["answer_double_sided"] = *req.AnswerDoubleSided
	}
	if req.AnswerVerticalArrangement != nil {
		updateMap["answer_vertical_arrangement"] = *req.AnswerVerticalArrangement
	}

	// 如果更新了关键配置，将配置应用到任务上以计算新状态
	if configChanged && task.GetActualStatus() != model.CorrectionTaskStatusCompleted {
		if req.NeedScanPaper != nil {
			task.NeedScanPaper = *req.NeedScanPaper
		}
		if req.PaperWithAnswer != nil {
			task.PaperWithAnswer = *req.PaperWithAnswer
		}

		// 获取根据配置计算的新状态
		actualStatus := task.GetActualStatus()
		if actualStatus != task.Status {
			updateMap["status"] = actualStatus
		}
	}

	if len(updateMap) > 0 {
		if err := model.UpdateCorrectionTask(db, taskID, updateMap); err != nil {
			logs.WithError(err).Error("更新任务设置失败")
			c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrDB, err)))
			return
		}
	}

	c.JSON(http.StatusOK, gin.H{})
}

type ListTaskPrinterResponse struct {
	PrinterID string `json:"printerId"`
	ExpiredAt string `json:"expiredAt"`
	Status    string `json:"status"`
	ScannedAt string `json:"scannedAt"`
	BindAt    string `json:"bindAt"`
}

func GetCorrectionTaskPrinterList(c *gin.Context) {
	logs := logger.GetLogger(c)

	// 验证用户身份
	token := utils.GetCtxAuthToken(c)
	if token == nil {
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.NewBizErrf(constant.ErrAuth, "Unauthorized")))
		return
	}

	// 获取任务ID
	taskID := c.Param("taskID")
	if taskID == "" {
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.NewBizErrf(constant.ErrParam, "任务ID不能为空")))
		return
	}

	db := gormclient.GetMultiDB("")
	taskPrinters, err := model.ListPrinterAuthsByTaskID(db, taskID)
	if err != nil {
		logs.WithError(err).Error("fail to get task printers")
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	if taskPrinters == nil {
		resp := make([]*ListTaskPrinterResponse, 0)
		c.JSON(http.StatusOK, commonModel.NewSuccessResponse(resp))
		return
	}

	list := make([]*ListTaskPrinterResponse, 0, len(taskPrinters))
	for _, printer := range taskPrinters {
		list = append(list, &ListTaskPrinterResponse{
			PrinterID: printer.UUID.String(),
			ExpiredAt: printer.ExpiredAt.Format("2006-01-02 15:04:05"),
			Status:    printer.Status,
			ScannedAt: printer.ScannedAt.Format("2006-01-02 15:04:05"),
			BindAt:    printer.BindAt.Format("2006-01-02 15:04:05"),
		})
	}
	c.JSON(http.StatusOK, commonModel.NewSuccessResponse(list))
}

func RemoveTaskPrinter(c *gin.Context) {
	logs := logger.GetLogger(c)

	// 验证用户身份
	token := utils.GetCtxAuthToken(c)
	if token == nil {
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.NewBizErrf(constant.ErrAuth, "Unauthorized")))
		return
	}

	// 获取任务ID
	taskID := c.Param("taskID")
	if taskID == "" {
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.NewBizErrf(constant.ErrParam, "任务ID不能为空")))
		return
	}

	printerID := c.Param("printerID")
	if printerID == "" {
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.NewBizErrf(constant.ErrParam, "打印机ID不能为空")))
		return
	}

	db := gormclient.GetMultiDB("")
	err := model.DeletePrinterAuth(db, printerID)
	if err != nil {
		logs.WithError(err).Error("fail to remove task printer")
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	c.JSON(http.StatusOK, commonModel.NewSuccessResponse(gin.H{}))
}
