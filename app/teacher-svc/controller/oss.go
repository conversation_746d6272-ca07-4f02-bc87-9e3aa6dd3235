package controller

import (
	"context"
	"crypto/hmac"
	"crypto/sha1"
	"encoding/base64"
	"encoding/json"
	"hash"
	"io"
	"net/http"
	"strings"
	"time"

	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/teacher-svc/constant"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/teacher-svc/middleware"
	"codeup.aliyun.com/level-up/public/common/bizerr"
	"codeup.aliyun.com/level-up/public/common/logger"
	commonModel "codeup.aliyun.com/level-up/public/common/model"
	"codeup.aliyun.com/level-up/public/common/utils"
	"github.com/aliyun/aliyun-oss-go-sdk/oss"
	"github.com/gin-gonic/gin"
	"github.com/rs/xid"
	"github.com/spf13/viper"
)

const (
	PublicOssUploadTTL   = time.Minute
	GetPublicOssTTL      = time.Hour
	PublicMaxSizeInBytes = 1024 * 1024 * 10
)

type GetOssTokenResponse struct {
	AccessKeyId string `json:"access_key_id"`
	Policy      string `json:"policy"`
	Signature   string `json:"signature"`
	Host        string `json:"host"`
	ObjectKey   string `json:"object_key"` // 用户只允许上传到这个object key
	AccessURL   string `json:"access_url"`
}

type GetPublicOssFileResponse struct {
	AccessURL string `json:"access_url"`
}

type ConfigStruct struct {
	Expiration string  `json:"expiration"`
	Conditions [][]any `json:"conditions"`
}

func GetOssTokenHandler(printerAuth bool) gin.HandlerFunc {
	return func(ginCtx *gin.Context) {
		logs := logger.GetLogger(ginCtx)
		token := utils.GetCtxAuthToken(ginCtx)
		if !printerAuth && token == nil {
			ginCtx.JSON(http.StatusUnauthorized, gin.H{})
			return
		}
		if printerAuth {
			auth := middleware.GetPrinterAuth(ginCtx)
			if auth == nil {
				ginCtx.JSON(http.StatusUnauthorized, gin.H{})
				return
			}
		}

		ext, ok := ginCtx.GetQuery("ext")
		if !ok {
			ginCtx.JSON(http.StatusBadRequest, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrParam, "ext is required")))
			return
		}
		// 获取临时凭证信息
		fileID := xid.New().String() + "." + ext
		fileID = viper.GetString("aliyun.oss.folder.public") + "/" + fileID
		id := viper.GetString("aliyun.access_key.id")
		secret := viper.GetString("aliyun.access_key.secret")
		host := viper.GetString("aliyun.oss.host.default")
		policy, sign, err := getOssPolicyToken(PublicOssUploadTTL, fileID, secret, PublicMaxSizeInBytes)
		if err != nil {
			logs.WithError(err).Errorf("fail to get oss policy token")
			ginCtx.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(err))
			return
		}
		// 获取图片访问链接
		endpoint := viper.GetString("aliyun.oss.endpoint.public")
		bucketName := viper.GetString("aliyun.oss.bucket.default")
		fileURL, err := signOSSUrl(ginCtx, endpoint, bucketName, fileID, true, GetPublicOssTTL, []oss.Option{
			oss.ContentDisposition("inline"),
		})
		if err != nil {
			logs.WithError(err).Errorf("fail to sign oss url %s", fileID)
			ginCtx.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrOSS, err)))
			return
		}
		ginCtx.JSON(http.StatusOK, commonModel.NewSuccessResponse(GetOssTokenResponse{
			AccessKeyId: id,
			Policy:      policy,
			Signature:   sign,
			Host:        host,
			ObjectKey:   fileID,
			AccessURL:   fileURL,
		}))
	}
}

func GetPublicOSSFileHandler(ginCtx *gin.Context) {
	logs := logger.GetLogger(ginCtx)
	token := utils.GetCtxAuthToken(ginCtx)
	if token == nil {
		ginCtx.JSON(http.StatusUnauthorized, gin.H{})
		return
	}

	fileID := ginCtx.Query("key")
	if !strings.HasPrefix(fileID, viper.GetString("aliyun.oss.folder.public")+"/") {
		logs.Errorf("invalid file id %s", fileID)
		ginCtx.JSON(http.StatusBadRequest, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrParam, "key is invalid")))
		return
	}
	if strings.Contains(fileID, "..") {
		logs.Errorf("invalid file id %s", fileID)
		ginCtx.JSON(http.StatusBadRequest, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrParam, "key is invalid")))
		return
	}
	if strings.HasSuffix(fileID, "/") {
		logs.Errorf("invalid file id %s", fileID)
		ginCtx.JSON(http.StatusBadRequest, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrParam, "key is invalid")))
		return
	}
	// 获取图片访问链接
	endpoint := viper.GetString("aliyun.oss.endpoint.public")
	bucketName := viper.GetString("aliyun.oss.bucket.default")
	fileURL, err := signOSSUrl(ginCtx, endpoint, bucketName, fileID, true, GetPublicOssTTL, []oss.Option{
		oss.ContentDisposition("inline"),
	})
	if err != nil {
		logs.WithError(err).Errorf("fail to sign oss url %s", fileID)
		ginCtx.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrOSS, err)))
		return
	}
	ginCtx.JSON(http.StatusOK, GetPublicOssFileResponse{
		AccessURL: fileURL,
	})
}

func getGmtIso8601(expire_end int64) string {
	var tokenExpire = time.Unix(expire_end, 0).UTC().Format("2006-01-02T15:04:05Z")
	return tokenExpire
}

func getOssPolicyToken(exp time.Duration, uploadDir string, accessKeySecret string, sizeInBytes int64) (string, string, error) {
	now := time.Now().Unix()
	expireEnd := now + int64(exp.Seconds())
	var tokenExpire = getGmtIso8601(expireEnd)

	//create post policy json
	var config ConfigStruct
	config.Expiration = tokenExpire
	config.Conditions = [][]any{
		{
			"starts-with", "$key", uploadDir,
		},
		{
			"content-length-range", 0, sizeInBytes,
		},
	}

	//calucate signature
	result, err := json.Marshal(config)
	if err != nil {
		return "", "", err
	}
	debyte := base64.StdEncoding.EncodeToString(result)
	h := hmac.New(func() hash.Hash { return sha1.New() }, []byte(accessKeySecret))
	io.WriteString(h, debyte)
	signedStr := base64.StdEncoding.EncodeToString(h.Sum(nil))

	return debyte, signedStr, nil
}

func getOSSClient(endpoint string, useCDN bool) (*oss.Client, error) {
	id := viper.GetString("aliyun.access_key.id")
	secret := viper.GetString("aliyun.access_key.secret")
	if useCDN {
		return oss.New(endpoint, id, secret, oss.UseCname(true))
	} else {
		return oss.New(endpoint, id, secret)
	}
}

func signOSSUrl(ctx context.Context, endpoint, bucketName, objectKey string, useCdn bool, exp time.Duration, options []oss.Option) (string, error) {
	logs := logger.GetLogger(ctx)
	client, err := getOSSClient(endpoint, useCdn)
	if err != nil {
		logs.WithError(err).Errorf("fail to get oss client")
		return "", bizerr.WrapErr(constant.ErrOSS, err)
	}
	bucket, err := client.Bucket(bucketName)
	if err != nil {
		logs.WithError(err).Errorf("fail to get oss bucket %s", bucketName)
		return "", bizerr.WrapErr(constant.ErrOSS, err)
	}
	url, err := bucket.SignURL(objectKey, oss.HTTPGet, int64(exp.Seconds()), options...)
	if err != nil {
		logs.WithError(err).Errorf("fail to get oss url %s", objectKey)
		return "", bizerr.WrapErr(constant.ErrOSS, err)
	}
	return url, nil
}
