package controller

import (
	"fmt"
	"net/http"
	"time"

	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/teacher-svc/constant"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/model"
	"codeup.aliyun.com/level-up/public/common/bizerr"
	gormclient "codeup.aliyun.com/level-up/public/common/clients/gorm"
	rclient "codeup.aliyun.com/level-up/public/common/clients/redis"
	"codeup.aliyun.com/level-up/public/common/logger"
	commonModel "codeup.aliyun.com/level-up/public/common/model"
	"codeup.aliyun.com/level-up/public/common/utils"
	"github.com/gin-gonic/gin"
	uuid "github.com/satori/go.uuid"
	"github.com/spf13/viper"
)

const (
	QRCodeExpireTime    = time.Minute * 1    // 二维码有效期1分钟
	AuthTokenExpireTime = time.Hour * 24 * 3 // 授权令牌有效期3天
	AfterScanExpireTime = time.Minute * 3    // 扫描二维码后，留给用户 3 分钟时间选择任务
)

// GetQRCodeResponse 获取二维码响应
type GetQRCodeResponse struct {
	QRCodeID string `json:"qr_code_id"` // 二维码ID
	Content  string `json:"content"`    // 二维码内容
	ExpireAt int64  `json:"expire_at"`  // 过期时间戳
}

type ScanPrinterRequest struct {
	QRCodeID string `json:"qr_code_id" binding:"required"` // 二维码ID
}

// BindTaskRequest 绑定任务请求
type BindTaskRequest struct {
	QRCodeID string `json:"qr_code_id" binding:"required"` // 二维码ID
	TaskID   string `json:"task_id" binding:"required"`    // 任务ID
}

// BindTaskResponse 绑定任务响应
type BindTaskResponse struct {
	Token    string `json:"token"`     // 令牌
	ExpireAt int64  `json:"expire_at"` // 过期时间戳
}

// GenerateQRCode 生成二维码
func GenerateQRCode(c *gin.Context) {
	logs := logger.GetLogger(c)

	// 生成二维码ID
	qrCodeID := uuid.NewV4().String()
	now := time.Now()
	expiredAt := now.Add(QRCodeExpireTime)

	// 将二维码ID存入Redis，有效期30秒
	key := rclient.GetUserTokenKey(constant.PrinterAuthTokenPrefix, qrCodeID)
	client := rclient.GetRedisClient()
	if err := client.SetEx(c, key, qrCodeID, time.Second*30).Err(); err != nil {
		logs.WithError(err).Errorf("将二维码保存到Redis失败 %s", key)
		c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	// 手机扫描二维码后，直接跳转链接
	scanURL := fmt.Sprintf("%s%s", viper.GetString("biz.scan_host"), qrCodeID)

	c.JSON(http.StatusOK, commonModel.NewSuccessResponse(&GetQRCodeResponse{
		QRCodeID: qrCodeID,
		Content:  scanURL,
		ExpireAt: expiredAt.Unix(),
	}))
}

func ScanPrinter(c *gin.Context) {
	logs := logger.GetLogger(c)

	// 验证用户身份
	token := utils.GetCtxAuthToken(c)
	if token == nil {
		c.JSON(http.StatusUnauthorized, gin.H{})
		return
	}

	var req ScanPrinterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logs.WithError(err).Error("解析扫描任务参数失败")
		c.JSON(http.StatusBadRequest, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrParam, err)))
		return
	}

	db := gormclient.GetMultiDB("")
	// 创建一条任务记录
	now := time.Now()
	expiredAt := now.Add(AfterScanExpireTime)
	auth := &model.PrinterAuth{
		UUID:      uuid.NewV4(),
		Status:    model.PrinterAuthStatusScanned,
		QRCodeID:  req.QRCodeID,
		ExpiredAt: expiredAt,
		ScannedAt: &now,
	}
	if err := model.CreatePrinterAuth(db, auth); err != nil {
		logs.WithError(err).Error("创建打印机授权记录失败")
		c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	c.JSON(http.StatusOK, commonModel.NewSuccessResponse(gin.H{
		"success": true,
	}))
}

// BindTask 绑定任务到二维码
func BindTask(c *gin.Context) {
	logs := logger.GetLogger(c)

	// 验证用户身份
	token := utils.GetCtxAuthToken(c)
	if token == nil {
		c.JSON(http.StatusUnauthorized, gin.H{})
		return
	}

	// 解析请求参数
	var req BindTaskRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logs.WithError(err).Error("解析绑定任务参数失败")
		c.JSON(http.StatusBadRequest, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrParam, err)))
		return
	}

	// 验证任务是否存在且属于当前用户
	db := gormclient.GetMultiDB("")
	task, err := model.GetCorrectionTask(db, req.TaskID)
	if err != nil {
		logs.WithError(err).Error("获取批改任务失败")
		c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	if task == nil {
		c.JSON(http.StatusNotFound, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrNotFound, "任务不存在")))
		return
	}

	if task.UserID.String() != token.UserID {
		c.JSON(http.StatusForbidden, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrAuth, "无权访问此任务")))
		return
	}
	// 从数据库中获取记录
	auth, err := model.GetPrinterAuthByQRCodeID(db, req.QRCodeID)
	if err != nil || auth == nil {
		logs.WithError(err).Errorf("fail to get printer auth with qr code %s, err %v", req.QRCodeID, err)
		c.JSON(http.StatusNotFound, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrNotFound, "授权记录不存在")))
		return
	}
	now := time.Now()
	if auth.ExpiredAt.Before(now) {
		c.JSON(http.StatusForbidden, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrAuth, "二维码已失效")))
		return
	}

	// 生成授权令牌
	authToken := uuid.NewV4().String()
	expiredAt := now.Add(AuthTokenExpireTime)
	if err := model.UpdateNotScannedPrinterAuth(db, auth.UUID.String(), map[string]any{
		"status":     model.PrinterAuthStatusBind,
		"token":      authToken,
		"expired_at": expiredAt,
		"bind_at":    now,
		"task_id":    req.TaskID,
	}); err != nil {
		logs.WithError(err).Errorf("fail to update printer auth %s", auth.UUID.String())
		c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	c.JSON(http.StatusOK, commonModel.NewSuccessResponse(&BindTaskResponse{
		Token:    auth.Token,
		ExpireAt: auth.ExpiredAt.Unix(),
	}))
}

// GetQRCodeInfo 获取二维码信息
func GetQRCodeInfo(c *gin.Context) {
	logs := logger.GetLogger(c)

	// 获取二维码ID
	qrCodeID := c.Query("qr_code_id")
	if qrCodeID == "" {
		c.JSON(http.StatusBadRequest, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrParam, "二维码ID不能为空")))
		return
	}

	db := gormclient.GetMultiDB("")
	auth, err := model.GetPrinterAuthByQRCodeID(db, qrCodeID)
	if err != nil {
		logs.WithError(err).Error("获取二维码信息失败")
		c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	// 如果数据库中不存在记录，说明二维码未绑定任务
	if auth == nil {
		c.JSON(http.StatusBadRequest, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrParam, "二维码未绑定任务")))
		return
	}

	// 返回二维码信息
	resp := gin.H{
		"token":     auth.Token,
		"status":    auth.Status,
		"expire_at": auth.ExpiredAt.Unix(),
	}
	if auth.TaskID != uuid.Nil {
		resp["task_id"] = auth.TaskID.String()
	}
	c.JSON(http.StatusOK, commonModel.NewSuccessResponse(resp))
}
