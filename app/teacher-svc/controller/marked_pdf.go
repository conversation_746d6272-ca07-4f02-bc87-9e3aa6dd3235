package controller

import (
	"net/http"
	"os"
	"time"

	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/teacher-svc/constant"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/teacher-svc/service"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/teacher-svc/utils"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/model"
	"codeup.aliyun.com/level-up/public/common/bizerr"
	gormclient "codeup.aliyun.com/level-up/public/common/clients/gorm"
	"codeup.aliyun.com/level-up/public/common/logger"
	commonModel "codeup.aliyun.com/level-up/public/common/model"
	commonUtils "codeup.aliyun.com/level-up/public/common/utils"
	"github.com/gin-gonic/gin"
	"github.com/rs/xid"
	uuid "github.com/satori/go.uuid"
	"github.com/spf13/viper"
)

// GetMarkedPDFResponse 获取留痕PDF响应
type GetMarkedPDFResponse struct {
	URL string `json:"url"` // PDF文件URL
}

// GetMarkedPDF 获取整个任务的留痕PDF
func GetMarkedPDF(c *gin.Context) {
	logs := logger.GetLogger(c)

	// 验证用户身份
	token := commonUtils.GetCtxAuthToken(c)
	if token == nil {
		c.JSON(http.StatusUnauthorized, gin.H{})
		return
	}

	// 获取任务ID
	taskID := c.Param("taskID")
	if taskID == "" {
		logs.Error("任务ID不能为空")
		c.JSON(http.StatusBadRequest, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrParam, "任务ID不能为空")))
		return
	}

	// 获取任务
	db := gormclient.GetMultiDB("")
	task, err := model.GetCorrectionTask(db, taskID)
	if err != nil {
		logs.WithError(err).Error("获取批改任务失败")
		c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	if task == nil {
		c.JSON(http.StatusNotFound, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrNotFound, "任务不存在")))
		return
	}

	// 验证任务所有者
	if task.UserID.String() != token.UserID {
		logs.Infof("任务 %s 不属于用户 %s", taskID, token.UserID)
		c.JSON(http.StatusForbidden, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrAuth, "无权访问此任务")))
		return
	}

	// 检查是否需要重新生成PDF
	var pdfURL string
	if task.TaskMarkedFileID != "" && !task.MarkedFileChanged {
		// 如果已有PDF且标记未改变，直接返回URL
		endpoint := viper.GetString("aliyun.oss.endpoint.public")
		bucketName := viper.GetString("aliyun.oss.bucket.default")
		pdfURL, err = signOSSUrl(c, endpoint, bucketName, task.TaskMarkedFileID, true, time.Hour, nil)
		if err != nil {
			logs.WithError(err).Errorf("获取PDF URL失败，fileID: %s", task.TaskMarkedFileID)
			// 继续生成新的PDF
		} else {
			// 返回现有PDF的URL
			c.JSON(http.StatusOK, commonModel.NewSuccessResponse(&GetMarkedPDFResponse{
				URL: pdfURL,
			}))
			return
		}
	}

	// 获取任务下所有队列项
	items, err := model.GetMarkedUploadQueueItemsByTaskID(db, uuid.FromStringOrNil(taskID))
	if err != nil {
		logs.WithError(err).Error("获取队列项失败")
		c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	if len(items) == 0 {
		logs.Info("没有找到队列项")
		c.JSON(http.StatusNotFound, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrNotFound, "没有找到队列项")))
		return
	}

	service.SortSheetItems(c, items)
	// 获取所有图片的URL
	endpoint := viper.GetString("aliyun.oss.endpoint.public")
	bucketName := viper.GetString("aliyun.oss.bucket.default")
	imageURLs := make([]string, 0, len(items))

	for _, item := range items {
		var fileURL string
		var err error

		// 优先使用标记后的图片
		if item.MarkedFileID == "" {
			logs.Infof("队列项 %s 没有标记图片", item.UUID.String())
			c.JSON(http.StatusBadRequest, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrParam, "请先完成原卷留痕操作")))
			return
		}
		fileURL, err = signOSSUrl(c, endpoint, bucketName, item.MarkedFileID, true, time.Hour, nil)
		if err != nil {
			logs.WithError(err).Errorf("获取标记图片URL失败，fileID: %s", item.MarkedFileID)
			// 如果获取标记图片失败，尝试使用原始图片
			if item.FileID != "" {
				fileURL, err = signOSSUrl(c, endpoint, bucketName, item.FileID, true, time.Hour, nil)
				if err != nil {
					logs.WithError(err).Errorf("获取原始图片URL失败，fileID: %s", item.FileID)
					continue
				}
			} else {
				continue
			}
		}

		imageURLs = append(imageURLs, fileURL)
	}

	if len(imageURLs) == 0 {
		logs.Info("没有找到有效的图片")
		c.JSON(http.StatusNotFound, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrNotFound, "没有找到有效的图片")))
		return
	}

	// 生成PDF
	pdfPath, err := utils.GeneratePDFFromImages(c, imageURLs)
	if err != nil {
		logs.WithError(err).Error("生成PDF失败")
		c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrServer, err)))
		return
	}
	defer os.Remove(pdfPath) // 清理临时文件

	// 上传PDF
	pdfFileID := xid.New().String() + ".pdf"
	pdfFileID = viper.GetString("aliyun.oss.folder.public") + "/" + pdfFileID

	if err := uploadImageToAliyun(c, pdfPath, pdfFileID); err != nil {
		logs.WithError(err).Error("上传PDF失败")
		c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrServer, err)))
		return
	}

	// 更新任务的TaskMarkedFileID和MarkedFileChanged
	if err := model.UpdateCorrectionTask(db, taskID, map[string]interface{}{
		"task_marked_file_id": pdfFileID,
		"marked_file_changed": false,
	}); err != nil {
		logs.WithError(err).Error("更新任务PDF文件ID失败")
		// 继续返回URL，不中断流程
	}

	// 获取PDF的URL
	pdfURL, err = signOSSUrl(c, endpoint, bucketName, pdfFileID, true, time.Hour, nil)
	if err != nil {
		logs.WithError(err).Errorf("获取PDF URL失败，fileID: %s", pdfFileID)
		c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrServer, err)))
		return
	}

	// 返回PDF的URL
	c.JSON(http.StatusOK, commonModel.NewSuccessResponse(&GetMarkedPDFResponse{
		URL: pdfURL,
	}))
}

// GetMarkedPDF 获取整个任务的留痕PDF
func GetSheetMarkedPDF(c *gin.Context) {
	logs := logger.GetLogger(c)

	// 验证用户身份
	token := commonUtils.GetCtxAuthToken(c)
	if token == nil {
		c.JSON(http.StatusUnauthorized, gin.H{})
		return
	}

	// 获取任务ID
	sheetID := c.Param("sheetID")
	if sheetID == "" {
		logs.Error("答题卡ID不能为空")
		c.JSON(http.StatusBadRequest, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrParam, "答题卡ID不能为空")))
		return
	}

	// 获取任务
	db := gormclient.GetMultiDB("")
	sheet, err := model.GetSheetByID(db, sheetID)
	if err != nil {
		logs.WithError(err).Errorf("fail to get sheet %s", sheetID)
		c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}
	if sheet == nil {
		c.JSON(http.StatusNotFound, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrNotFound, "答题卡不存在")))
		return
	}

	// 验证任务所有者
	task, err := model.GetCorrectionTask(db, sheet.TaskID.String())
	if err != nil {
		logs.WithError(err).Error("获取批改任务失败")
		c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}
	if task.UserID.String() != token.UserID {
		logs.Infof("任务 %s 不属于用户 %s", sheet.TaskID.String(), token.UserID)
		c.JSON(http.StatusForbidden, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrAuth, "无权访问此任务")))
		return
	}

	var pdfURL string
	// 获取任务下所有队列项
	items, err := model.GetUploadQueueItemsBySheetID(db, sheetID)
	if err != nil {
		logs.WithError(err).Error("获取队列项失败")
		c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	if len(items) == 0 {
		logs.Info("没有找到队列项")
		c.JSON(http.StatusNotFound, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrNotFound, "没有找到队列项")))
		return
	}
	service.SortSheetItems(c, items)

	// 获取所有图片的URL
	endpoint := viper.GetString("aliyun.oss.endpoint.public")
	bucketName := viper.GetString("aliyun.oss.bucket.default")
	imageURLs := make([]string, 0, len(items))

	for _, item := range items {
		var fileURL string
		var err error

		// 优先使用标记后的图片
		if item.MarkedFileID == "" {
			logs.Infof("队列项 %s 没有标记图片", item.UUID.String())
			c.JSON(http.StatusBadRequest, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrParam, "请先完成原卷留痕操作")))
			return
		}
		fileURL, err = signOSSUrl(c, endpoint, bucketName, item.MarkedFileID, true, time.Hour, nil)
		if err != nil {
			logs.WithError(err).Errorf("获取标记图片URL失败，fileID: %s", item.MarkedFileID)
			// 如果获取标记图片失败，尝试使用原始图片
			if item.FileID != "" {
				fileURL, err = signOSSUrl(c, endpoint, bucketName, item.FileID, true, time.Hour, nil)
				if err != nil {
					logs.WithError(err).Errorf("获取原始图片URL失败，fileID: %s", item.FileID)
					continue
				}
			} else {
				continue
			}
		}

		imageURLs = append(imageURLs, fileURL)
	}

	if len(imageURLs) == 0 {
		logs.Info("没有找到有效的图片")
		c.JSON(http.StatusNotFound, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrNotFound, "没有找到有效的图片")))
		return
	}

	// 生成PDF
	pdfPath, err := utils.GeneratePDFFromImages(c, imageURLs)
	if err != nil {
		logs.WithError(err).Error("生成PDF失败")
		c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrServer, err)))
		return
	}
	defer os.Remove(pdfPath) // 清理临时文件

	// 上传PDF
	pdfFileID := xid.New().String() + ".pdf"
	pdfFileID = viper.GetString("aliyun.oss.folder.public") + "/" + pdfFileID

	if err := uploadImageToAliyun(c, pdfPath, pdfFileID); err != nil {
		logs.WithError(err).Error("上传PDF失败")
		c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrServer, err)))
		return
	}

	// 获取PDF的URL
	pdfURL, err = signOSSUrl(c, endpoint, bucketName, pdfFileID, true, time.Hour, nil)
	if err != nil {
		logs.WithError(err).Errorf("获取PDF URL失败，fileID: %s", pdfFileID)
		c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrServer, err)))
		return
	}

	// 返回PDF的URL
	c.JSON(http.StatusOK, commonModel.NewSuccessResponse(&GetMarkedPDFResponse{
		URL: pdfURL,
	}))
}
