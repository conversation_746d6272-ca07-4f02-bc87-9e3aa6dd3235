package middleware

import (
	"net/http"
	"strings"

	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/teacher-svc/constant"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/model"
	"codeup.aliyun.com/level-up/public/common/bizerr"
	gormclient "codeup.aliyun.com/level-up/public/common/clients/gorm"
	"codeup.aliyun.com/level-up/public/common/logger"
	"github.com/gin-gonic/gin"
)

// 打印机授权信息键
const PrinterAuthKey = "PrinterAuth"

// PrinterAuth 打印机授权中间件
func PrinterAuth() gin.HandlerFunc {
	return func(c *gin.Context) {
		logs := logger.GetLogger(c)

		// 从请求头获取Token
		token := c.GetHeader(constant.PrinterAuthHeaderKey)
		if token == "" {
			logs.Error("打印机授权Token不存在")
			c.JSON(http.StatusUnauthorized, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrAuth, "missing printer auth token")))
			c.Abort()
			return
		}
		token = strings.TrimPrefix(token, "Bearer ")

		// 验证Token
		db := gormclient.GetMultiDB("")
		auth, err := model.GetPrinterAuthByToken(db, token)
		if err != nil {
			logs.WithError(err).Error("获取打印机授权信息失败")
			c.JSON(http.StatusInternalServerError, bizerr.ToBaseResp(bizerr.WrapErr(constant.ErrDB, err)))
			c.Abort()
			return
		}

		if auth == nil {
			logs.Error("打印机授权不存在")
			c.JSON(http.StatusUnauthorized, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrAuth, "invalid printer auth token")))
			c.Abort()
			return
		}

		if auth.IsExpired() {
			logs.Error("打印机授权已过期")
			c.JSON(http.StatusUnauthorized, bizerr.ToBaseResp(bizerr.NewBizErrf(constant.ErrAuth, "expired printer auth token")))
			c.Abort()
			return
		}

		// 验证成功，将打印机授权存入上下文
		c.Set(PrinterAuthKey, auth)
		c.Next()
	}
}

// GetPrinterAuth 从上下文获取打印机授权信息
func GetPrinterAuth(c *gin.Context) *model.PrinterAuth {
	value, exists := c.Get(PrinterAuthKey)
	if !exists {
		return nil
	}

	auth, ok := value.(*model.PrinterAuth)
	if !ok {
		return nil
	}

	return auth
}

// GetTaskIDFromPrinterAuth 从上下文获取任务ID
func GetTaskIDFromPrinterAuth(c *gin.Context) string {
	auth := GetPrinterAuth(c)
	if auth == nil {
		return ""
	}

	return auth.TaskID.String()
}
