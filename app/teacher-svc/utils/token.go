package utils

import (
	"context"
	"time"

	commonmodel "codeup.aliyun.com/level-up/public/common/model"
	uuid "github.com/satori/go.uuid"
)

const (
	MaxUserTokenExpTime = time.Hour * 24 * 7
)

func GenerateUserToken(ctx context.Context, userID string) *commonmodel.AuthTokenInRedis {
	// 生成token
	token := &commonmodel.AuthTokenInRedis{
		Token:     uuid.NewV1().String(),
		ExpiredAt: time.Now().Add(MaxUserTokenExpTime).Unix(),
	}
	return token
}
