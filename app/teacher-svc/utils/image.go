package utils

import (
	"bytes"
	"context"
	"fmt"
	"image"
	"image/color"
	"image/draw"
	"image/gif"
	"image/jpeg"
	"image/png"
	"io"
	"net/http"
	"os"

	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/teacher-svc/constant"
	"codeup.aliyun.com/level-up/public/common/bizerr"
	"codeup.aliyun.com/level-up/public/common/logger"
)

func init() {
	// 注册图片格式
	image.RegisterFormat("png", "PNG", png.Decode, png.DecodeConfig)
	image.RegisterFormat("jpeg", "JPEG", jpeg.Decode, jpeg.DecodeConfig)
	image.RegisterFormat("jpg", "JPEG", jpeg.Decode, jpeg.DecodeConfig)
	image.RegisterFormat("gif", "GIF", gif.Decode, gif.DecodeConfig)
}

// CombineImagesHorizontally 下载多个图片URL并横向拼接，生成jpg后上传到OSS
func CombineImagesHorizontally(ctx context.Context, imageURLs []string) (string, error) {
	logs := logger.GetLogger(ctx)

	if len(imageURLs) == 0 {
		return "", fmt.Errorf("no images to combine")
	}

	// 1. 下载所有图片
	var images []image.Image
	var totalWidth, maxHeight int

	for _, url := range imageURLs {
		// 下载图片
		resp, err := http.Get(url)
		if err != nil {
			logs.WithError(err).Errorf("fail to download image from url: %s", url)
			return "", bizerr.WrapErr(constant.ErrServer, err)
		}
		defer resp.Body.Close()

		if resp.StatusCode != http.StatusOK {
			logs.Errorf("fail to download image, status code: %d, url: %s", resp.StatusCode, url)
			return "", bizerr.NewBizErrf(constant.ErrServer, "download image failed with status: "+resp.Status)
		}

		// 读取响应体内容
		bodyBytes, err := io.ReadAll(resp.Body)
		if err != nil {
			logs.WithError(err).Errorf("fail to read image data from url: %s", url)
			return "", bizerr.WrapErr(constant.ErrServer, err)
		}

		// 初始化解码图片
		var img image.Image

		// 使用通用解码函数
		img, _, err = image.Decode(bytes.NewReader(bodyBytes))
		if err != nil {
			logs.WithError(err).Errorf("fail to decode image from url: %s, trying specific decoders", url)

			// 尝试特定格式解码
			var decodeErr error

			// 尝试PNG
			imgReader := bytes.NewReader(bodyBytes)
			img, decodeErr = png.Decode(imgReader)
			if decodeErr == nil {
				goto ImageDecoded
			}

			// 尝试JPEG
			imgReader.Reset(bodyBytes)
			img, decodeErr = jpeg.Decode(imgReader)
			if decodeErr == nil {
				goto ImageDecoded
			}

			// 尝试GIF
			imgReader.Reset(bodyBytes)
			img, decodeErr = gif.Decode(imgReader)
			if decodeErr == nil {
				goto ImageDecoded
			}

			// 如果所有尝试都失败
			logs.WithError(err).Errorf("all image format decoders failed for url: %s", url)
			return "", bizerr.WrapErr(constant.ErrServer, err)
		}

	ImageDecoded:
		// 累计总宽度和找出最大高度
		bounds := img.Bounds()
		totalWidth += bounds.Dx()
		if bounds.Dy() > maxHeight {
			maxHeight = bounds.Dy()
		}

		images = append(images, img)
	}

	// 2. 创建新图片 - 宽度是所有图片宽度之和，高度是最高图片的高度
	resultImg := image.NewRGBA(image.Rect(0, 0, totalWidth, maxHeight))

	// 3. 将图片横向拼接
	x := 0
	for _, img := range images {
		bounds := img.Bounds()
		// 将图片绘制到目标图片上
		for y := 0; y < bounds.Dy(); y++ {
			for ix := 0; ix < bounds.Dx(); ix++ {
				// 如果当前位置超出最大高度，则跳过
				if y >= maxHeight {
					continue
				}
				resultImg.Set(x+ix, y, img.At(bounds.Min.X+ix, bounds.Min.Y+y))
			}
		}
		x += bounds.Dx()
	}

	// 4. 生成临时文件用于存储结果图片
	tempFile, err := os.CreateTemp("", "combined-image-*.jpg")
	if err != nil {
		logs.WithError(err).Error("fail to create temp file")
		return "", bizerr.WrapErr(constant.ErrServer, err)
	}
	defer tempFile.Close()

	// 5. 编码为JPEG并写入临时文件
	if err := jpeg.Encode(tempFile, resultImg, &jpeg.Options{Quality: 90}); err != nil {
		logs.WithError(err).Error("fail to encode image to jpeg")
		return "", bizerr.WrapErr(constant.ErrServer, err)
	}

	return tempFile.Name(), nil
}

// BoxColor 表示方框的颜色类型
type BoxColor int

const (
	RedBox    BoxColor = iota // 红色框
	GreenBox                  // 绿色框
	YellowBox                 // 黄色框
)

// BoxCoordinate 表示方框的坐标
type BoxCoordinate struct {
	TopLeft     Point    // 左上角坐标
	BottomRight Point    // 右下角坐标
	Color       BoxColor // 方框颜色
}

// Point 表示坐标点
type Point struct {
	X int
	Y int
}

// DrawBoxesOnImage 在图片上绘制方框
// 返回临时文件路径
func DrawBoxesOnImage(ctx context.Context, imageURL string, boxes []BoxCoordinate) (string, error) {
	logs := logger.GetLogger(ctx)

	// 1. 下载图片
	resp, err := http.Get(imageURL)
	if err != nil {
		logs.WithError(err).Errorf("fail to download image from url: %s", imageURL)
		return "", bizerr.WrapErr(constant.ErrServer, err)
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		logs.Errorf("fail to download image, status code: %d, url: %s", resp.StatusCode, imageURL)
		return "", bizerr.NewBizErrf(constant.ErrServer, "download image failed with status: "+resp.Status)
	}

	// 读取响应体内容
	bodyBytes, err := io.ReadAll(resp.Body)
	if err != nil {
		logs.WithError(err).Errorf("fail to read image data from url: %s", imageURL)
		return "", bizerr.WrapErr(constant.ErrServer, err)
	}

	// 初始化解码图片
	var img image.Image

	// 使用通用解码函数
	img, _, err = image.Decode(bytes.NewReader(bodyBytes))
	if err != nil {
		logs.WithError(err).Errorf("fail to decode image from url: %s, trying specific decoders", imageURL)

		// 尝试特定格式解码
		var decodeErr error

		// 尝试PNG
		imgReader := bytes.NewReader(bodyBytes)
		img, decodeErr = png.Decode(imgReader)
		if decodeErr == nil {
			goto ImageDecoded
		}

		// 尝试JPEG
		imgReader.Reset(bodyBytes)
		img, decodeErr = jpeg.Decode(imgReader)
		if decodeErr == nil {
			goto ImageDecoded
		}

		// 尝试GIF
		imgReader.Reset(bodyBytes)
		img, decodeErr = gif.Decode(imgReader)
		if decodeErr == nil {
			goto ImageDecoded
		}

		// 如果所有尝试都失败
		logs.WithError(err).Errorf("all image format decoders failed for url: %s", imageURL)
		return "", bizerr.WrapErr(constant.ErrServer, err)
	}

ImageDecoded:
	// 2. 创建可编辑的图像
	bounds := img.Bounds()
	resultImg := image.NewRGBA(bounds)

	// 3. 将原图复制到新图像
	draw.Draw(resultImg, bounds, img, bounds.Min, draw.Src)

	// 4. 在图像上绘制方框
	imgWidth := bounds.Dx()
	imgHeight := bounds.Dy()

	for _, box := range boxes {
		// 跳过无效坐标
		if box.TopLeft.X == 0 && box.TopLeft.Y == 0 && box.BottomRight.X == 0 && box.BottomRight.Y == 0 {
			continue
		}

		// 根据指定的颜色类型选择颜色
		var boxColor color.RGBA
		switch box.Color {
		case GreenBox:
			boxColor = color.RGBA{0, 255, 0, 255} // 绿色
		case RedBox:
			boxColor = color.RGBA{255, 0, 0, 255} // 红色
		case YellowBox:
			boxColor = color.RGBA{255, 255, 0, 255} // 黄色
		default:
			boxColor = color.RGBA{0, 0, 255, 255} // 默认蓝色
		}

		// 缩放坐标：将坐标除以1000后乘以图片宽高
		scaledX1 := int(float64(box.TopLeft.X) / 1000 * float64(imgWidth))
		scaledY1 := int(float64(box.TopLeft.Y) / 1000 * float64(imgHeight))
		scaledX2 := int(float64(box.BottomRight.X) / 1000 * float64(imgWidth))
		scaledY2 := int(float64(box.BottomRight.Y) / 1000 * float64(imgHeight))

		// 绘制矩形框
		drawRectangle(resultImg, scaledX1, scaledY1, scaledX2, scaledY2, boxColor, 3)
	}

	// 5. 生成临时文件用于存储结果图片
	tempFile, err := os.CreateTemp("", "marked-image-*.jpg")
	if err != nil {
		logs.WithError(err).Error("fail to create temp file")
		return "", bizerr.WrapErr(constant.ErrServer, err)
	}
	defer tempFile.Close()

	// 6. 编码为JPEG并写入临时文件
	if err := jpeg.Encode(tempFile, resultImg, &jpeg.Options{Quality: 90}); err != nil {
		logs.WithError(err).Error("fail to encode image to jpeg")
		return "", bizerr.WrapErr(constant.ErrServer, err)
	}

	return tempFile.Name(), nil
}

// drawRectangle 在图像上绘制矩形
func drawRectangle(img *image.RGBA, x1, y1, x2, y2 int, col color.RGBA, thickness int) {
	// 绘制水平线
	for t := 0; t < thickness; t++ {
		// 上边框
		for x := x1; x <= x2; x++ {
			if y1+t >= 0 && y1+t < img.Bounds().Dy() && x >= 0 && x < img.Bounds().Dx() {
				img.Set(x, y1+t, col)
			}
		}
		// 下边框
		for x := x1; x <= x2; x++ {
			if y2-t >= 0 && y2-t < img.Bounds().Dy() && x >= 0 && x < img.Bounds().Dx() {
				img.Set(x, y2-t, col)
			}
		}
	}

	// 绘制垂直线
	for t := 0; t < thickness; t++ {
		// 左边框
		for y := y1; y <= y2; y++ {
			if x1+t >= 0 && x1+t < img.Bounds().Dx() && y >= 0 && y < img.Bounds().Dy() {
				img.Set(x1+t, y, col)
			}
		}
		// 右边框
		for y := y1; y <= y2; y++ {
			if x2-t >= 0 && x2-t < img.Bounds().Dx() && y >= 0 && y < img.Bounds().Dy() {
				img.Set(x2-t, y, col)
			}
		}
	}
}
