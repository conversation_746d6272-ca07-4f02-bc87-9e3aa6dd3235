package utils

import (
	"bytes"
	"context"
	"fmt"
	"image"
	"image/jpeg"
	"io"
	"net/http"
	"os"
	"path/filepath"

	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/teacher-svc/constant"
	"codeup.aliyun.com/level-up/public/common/bizerr"
	"codeup.aliyun.com/level-up/public/common/logger"
	"github.com/signintech/gopdf"
)

// GeneratePDFFromImages 从多个图片URL生成PDF文件
// 返回临时文件路径
func GeneratePDFFromImages(ctx context.Context, imageURLs []string) (string, error) {
	logs := logger.GetLogger(ctx)

	if len(imageURLs) == 0 {
		return "", fmt.Errorf("no images to combine")
	}

	// 创建临时目录用于存储下载的图片
	tempDir, err := os.MkdirTemp("", "pdf-images-")
	if err != nil {
		logs.WithError(err).Error("创建临时目录失败")
		return "", bizerr.WrapErr(constant.ErrServer, err)
	}
	defer os.RemoveAll(tempDir) // 清理临时目录

	// 下载所有图片到临时目录
	imagePaths := make([]string, 0, len(imageURLs))
	for i, url := range imageURLs {
		// 下载图片
		resp, err := http.Get(url)
		if err != nil {
			logs.WithError(err).Errorf("下载图片失败，URL: %s", url)
			continue
		}

		if resp.StatusCode != http.StatusOK {
			logs.Errorf("下载图片失败，状态码: %d，URL: %s", resp.StatusCode, url)
			resp.Body.Close()
			continue
		}

		// 读取响应体内容
		bodyBytes, err := io.ReadAll(resp.Body)
		resp.Body.Close()
		if err != nil {
			logs.WithError(err).Errorf("读取图片数据失败，URL: %s", url)
			continue
		}

		// 解码图片以获取尺寸
		img, _, err := image.Decode(bytes.NewReader(bodyBytes))
		if err != nil {
			logs.WithError(err).Errorf("解码图片失败，URL: %s", url)
			continue
		}

		// 保存图片到临时文件
		imgPath := filepath.Join(tempDir, fmt.Sprintf("image-%d.jpg", i))
		imgFile, err := os.Create(imgPath)
		if err != nil {
			logs.WithError(err).Errorf("创建临时图片文件失败，路径: %s", imgPath)
			continue
		}

		// 重新编码为JPEG并写入临时文件
		if err := jpeg.Encode(imgFile, img, &jpeg.Options{Quality: 90}); err != nil {
			logs.WithError(err).Errorf("编码图片失败，路径: %s", imgPath)
			imgFile.Close()
			continue
		}
		imgFile.Close()

		imagePaths = append(imagePaths, imgPath)
	}

	if len(imagePaths) == 0 {
		return "", fmt.Errorf("no images were successfully downloaded")
	}

	// 创建PDF
	// pdf := gofpdf.New("P", "mm", "A4", "")
	pdf := gopdf.GoPdf{}
	var pdfWidth, pdfHeight float64
	// 处理每个图片
	for idx, imgPath := range imagePaths {
		// 获取图片信息
		imgFile, err := os.Open(imgPath)
		if err != nil {
			logs.WithError(err).Errorf("打开图片文件失败，路径: %s", imgPath)
			continue
		}

		img, _, err := image.Decode(imgFile)
		imgFile.Close()
		if err != nil {
			logs.WithError(err).Errorf("解码图片失败，路径: %s", imgPath)
			continue
		}

		// 获取图片尺寸
		bounds := img.Bounds()
		imgWidth := float64(bounds.Dx())
		imgHeight := float64(bounds.Dy())
		if idx == 0 {
			pdfWidth, pdfHeight = imgWidth, imgHeight
			pdf.Start(gopdf.Config{PageSize: gopdf.Rect{W: pdfWidth, H: pdfHeight}})
		}
		pdf.AddPage()

		// 计算缩放比例，使图片适应页面
		widthRatio := pdfWidth / imgWidth
		heightRatio := pdfHeight / imgHeight
		ratio := widthRatio
		if widthRatio > heightRatio {
			ratio = heightRatio
		}

		// 计算缩放后的尺寸
		scaledWidth := imgWidth * ratio
		scaledHeight := imgHeight * ratio

		// 计算居中位置
		x := (pdfWidth - scaledWidth) / 2
		y := (pdfHeight - scaledHeight) / 2

		// 添加图片到PDF
		pdf.Image(imgPath, x, y, &gopdf.Rect{W: scaledWidth, H: scaledHeight})
	}

	// 保存PDF到临时文件
	tempFile, err := os.CreateTemp("", "marked-pdf-*.pdf")
	if err != nil {
		logs.WithError(err).Error("创建临时PDF文件失败")
		return "", bizerr.WrapErr(constant.ErrServer, err)
	}
	tempFile.Close() // 关闭文件，让PDF库可以写入

	// 保存PDF
	if err := pdf.WritePdf(tempFile.Name()); err != nil {
		logs.WithError(err).Error("保存PDF文件失败")
		os.Remove(tempFile.Name()) // 清理临时文件
		return "", bizerr.WrapErr(constant.ErrServer, err)
	}

	return tempFile.Name(), nil
}
