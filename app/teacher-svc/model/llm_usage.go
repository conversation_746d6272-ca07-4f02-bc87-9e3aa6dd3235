package model

// LLMUsageData represents token usage data for LLM calls
type LLMUsageData struct {
	// Model name used for the LLM call
	Model string `json:"model"`

	// Number of input tokens
	InputTokens int64 `json:"input_tokens"`

	// Number of output tokens
	OutputTokens int64 `json:"output_tokens"`

	// Total tokens used (input + output)
	TotalTokens int64 `json:"total_tokens"`

	// Additional context about the LLM call
	Context string `json:"context,omitempty"`

	// Raw response from the LLM
	RawResponse string `json:"raw_response,omitempty"`
}
