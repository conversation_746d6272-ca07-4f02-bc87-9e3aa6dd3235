package service

import (
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/management-svc/constant"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/model"
	"codeup.aliyun.com/level-up/public/common/bizerr"
	"gorm.io/gorm"
)

func CheckTenantUserRoleIsAdmin(db *gorm.DB, tenantId, userID string) (bool, error) {
	// check if user is tenant user
	tenantUser, err := model.GetTenantUserByTenantAndUserID(db, tenantId, userID)
	if err != nil {
		return false, bizerr.WrapErr(constant.ErrDB, err)
	}

	if tenantUser == nil {
		return false, nil
	}

	// check if user is admin
	tenantUserRoles, err := model.GetTenantUserRoleByTenantIDAndUserID(db, tenantId, userID)
	if err != nil {
		return false, bizerr.WrapErr(constant.ErrDB, err)
	}
	if tenantUserRoles == nil {
		return false, nil
	}
	if len(tenantUserRoles) == 0 {
		return false, nil
	}
	// check if user is admin
	isAdmin := false
	for _, role := range tenantUserRoles {
		if role.RoleCode == model.TenantRoleAdmin {
			isAdmin = true
			break
		}
	}

	if !isAdmin {
		return false, nil
	}

	return true, nil
}
