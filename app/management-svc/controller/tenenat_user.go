package controller

import (
	"net/http"

	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/management-svc/constant"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/management-svc/service"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/model"
	"codeup.aliyun.com/level-up/public/common/bizerr"
	gormClient "codeup.aliyun.com/level-up/public/common/clients/gorm"
	"codeup.aliyun.com/level-up/public/common/logger"
	commonModel "codeup.aliyun.com/level-up/public/common/model"
	commonUtils "codeup.aliyun.com/level-up/public/common/utils"
	"github.com/gin-gonic/gin"
)

type ListTenantUserRequest struct {
	TenantID string                 `form:"tenantId"`
	Status   model.TenantUserStatus `form:"status"`
	Page     int                    `form:"page"`
	Size     int                    `form:"size"`
}

type ListTenantUserResponse struct {
	TenantUserID string                 `json:"tenantUserId"`
	TenantID     string                 `json:"tenantId"`
	UserID       string                 `json:"userId"`
	Nickname     string                 `json:"nickname"`
	Phone        string                 `json:"phone"`
	Avatar       string                 `json:"avatar"`
	WxNickName   string                 `json:"wxNickName"`
	WxAvatarURL  string                 `json:"wxAvatarUrl"`
	Status       model.TenantUserStatus `json:"status"`
	RoleID       string                 `json:"roleId"`
	RoleName     string                 `json:"roleName"`
}

func ListTenantUser(c *gin.Context) {
	logs := logger.GetLogger(c)
	token := commonUtils.GetCtxAuthToken(c)
	if token == nil {
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.NewBizErrf(constant.ErrAuth, "Unauthorized")))
		return
	}

	var req ListTenantUserRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		logs.WithError(err).Error("Failed to bind query parameters")
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrParam, err)))
		return
	}

	if req.TenantID == "" {
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.NewBizErrf(constant.ErrParam, "Tenant ID is required")))
		return
	}

	db := gormClient.GetMultiDB("default")
	// check if user is tenant user
	tenantUser, err := model.GetTenantUserByTenantAndUserID(db, req.TenantID, token.UserID)
	if err != nil {
		logs.WithError(err).Error("Failed to get tenant user")
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	if tenantUser == nil {
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.NewBizErrf(constant.ErrAuth, "Unauthorized")))
		return
	}

	// check if user is admin
	isAdmin, err := service.CheckTenantUserRoleIsAdmin(db, req.TenantID, token.UserID)
	if err != nil {
		logs.WithError(err).Error("Failed to check tenant user role")
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	if !isAdmin {
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.NewBizErrf(constant.ErrAuth, "Unauthorized")))
		return
	}

	// get tenant users
	offset, limit := commonUtils.GetLimitOffset(req.Page, req.Size)
	println(offset, limit)
	tenantUsers, count, err := model.GetTenantUsers(db, offset, limit, &model.TenantUserFilter{
		TenantID: &req.TenantID,
		Status:   &req.Status,
	})

	if err != nil {
		logs.WithError(err).Error("Failed to get tenant users")
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	// map tenant users to response
	var response []ListTenantUserResponse
	for _, tenantUser := range tenantUsers {
		response = append(response, ListTenantUserResponse{
			TenantUserID: tenantUser.UUID.String(),
			TenantID:     tenantUser.TenantID.String(),
			UserID:       tenantUser.UserID.String(),
			Nickname:     tenantUser.Nickname,
			Phone:        tenantUser.Phone,
			Avatar:       tenantUser.Avatar,
			WxNickName:   tenantUser.WxNickName,
			WxAvatarURL:  tenantUser.WxAvatarURL,
			Status:       tenantUser.Status,
			RoleID:       tenantUser.RoleID.String(),
			RoleName:     tenantUser.RoleName,
		})
	}

	c.JSON(http.StatusOK, commonModel.NewSuccessListResponse(req.Page, req.Size, count, response))
}

type UpdateTenantUserStatusRequest struct {
	TenantUserID string `json:"tenantUserId" binding:"required"`
	Status       string `json:"status" binding:"required"`
}

func UpdateTenantUserStatus(c *gin.Context) {
	logs := logger.GetLogger(c)
	token := commonUtils.GetCtxAuthToken(c)
	if token == nil {
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.NewBizErrf(constant.ErrAuth, "Unauthorized")))
		return
	}

	var req UpdateTenantUserStatusRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logs.WithError(err).Error("Failed to bind JSON parameters")
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrParam, err)))
		return
	}

	db := gormClient.GetMultiDB("default")

	// check if user is tenant user
	tenantUser, err := model.GetTenantUserByUUID(db, req.TenantUserID)
	if err != nil {
		logs.WithError(err).Error("Failed to get tenant user")
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	if tenantUser == nil {
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.NewBizErrf(constant.ErrParam, "Tenant User not found")))
		return
	}

	// check if user is admin
	isAdmin, err := service.CheckTenantUserRoleIsAdmin(db, tenantUser.TenantID.String(), token.UserID)
	if err != nil {
		logs.WithError(err).Error("Failed to check tenant user role")
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	if !isAdmin {
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.NewBizErrf(constant.ErrAuth, "Unauthorized")))
		return
	}

	if tenantUser.Status == model.TenantUserStatus(req.Status) {
		c.JSON(http.StatusOK, commonModel.NewSuccessResponse(nil))
		return
	}
	tenantUser.Status = model.TenantUserStatus(req.Status)
	err = model.UpdateTenantUser(db, tenantUser)
	if err != nil {
		logs.WithError(err).Error("Failed to update tenant user status")
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	c.JSON(http.StatusOK, commonModel.NewSuccessResponse(nil))
}

type DeleteTenantUserRequest struct {
	UserID   string `json:"userId" binding:"required"`
	TenantID string `json:"tenantId" binding:"required"`
}

func DeleteTenantUser(c *gin.Context) {
	logs := logger.GetLogger(c)
	token := commonUtils.GetCtxAuthToken(c)
	if token == nil {
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.NewBizErrf(constant.ErrAuth, "Unauthorized")))
		return
	}

	var req DeleteTenantUserRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		logs.WithError(err).Error("Failed to bind JSON parameters")
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrParam, err)))
		return
	}

	db := gormClient.GetMultiDB("default")

	// check if user is admin
	isAdmin, err := service.CheckTenantUserRoleIsAdmin(db, req.TenantID, token.UserID)
	if err != nil {
		logs.WithError(err).Error("Failed to check tenant user role")
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	if !isAdmin {
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.NewBizErrf(constant.ErrAuth, "Unauthorized")))
		return
	}

	// check if user is tenant user
	tenantUser, err := model.GetTenantUserByTenantAndUserID(db, req.TenantID, req.UserID)
	if err != nil {
		logs.WithError(err).Error("Failed to get tenant user")
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	if tenantUser == nil {
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.NewBizErrf(constant.ErrParam, "Tenant User not found")))
		return
	}

	// Start transaction for atomicity
	tx := db.Begin()
	if tx.Error != nil {
		logs.WithError(tx.Error).Error("Failed to begin transaction")
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrDB, tx.Error)))
		return
	}

	// Delete tenant user roles first (foreign key relationship)
	err = model.DeleteTenantUserRolesByTenantUserID(tx, tenantUser.UUID.String())
	if err != nil {
		tx.Rollback()
		logs.WithError(err).Error("Failed to delete tenant user roles")
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	// Then delete the tenant user
	err = model.DeleteTenantUser(tx, tenantUser)
	if err != nil {
		tx.Rollback()
		logs.WithError(err).Error("Failed to delete tenant user")
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	// Commit the transaction
	if err := tx.Commit().Error; err != nil {
		logs.WithError(err).Error("Failed to commit transaction")
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	c.JSON(http.StatusOK, commonModel.NewSuccessResponse(nil))
}
