package controller

import (
	"errors"
	"net/http"

	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/management-svc/constant"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/management-svc/service"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/management-svc/utils"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/model"
	"codeup.aliyun.com/level-up/public/common/bizerr"
	"codeup.aliyun.com/level-up/public/common/clients/gorm"
	rclient "codeup.aliyun.com/level-up/public/common/clients/redis"
	"codeup.aliyun.com/level-up/public/common/logger"
	commonModel "codeup.aliyun.com/level-up/public/common/model"
	commonUtils "codeup.aliyun.com/level-up/public/common/utils"
	"github.com/gin-gonic/gin"
	"golang.org/x/crypto/bcrypt"
)

type LoginByPhoneAndPasswordRequest struct {
	Phone    string `json:"phone" binding:"required"`
	Password string `json:"password" binding:"required"`
}

type LoginByPhoneAndPasswordResponse struct {
	AccessToken string `json:"accessToken"`
	ExpireTime  int64  `json:"expireTime"`
}

// LoginByPhoneAndPasswordHandler handles user login
func LoginByPhoneAndPasswordHandler(c *gin.Context) {
	logs := logger.GetLogger(c)
	var req LoginByPhoneAndPasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrParam, err)))
		return
	}

	db := gorm.GetMultiDB("default")
	user, err := model.GetUserWithPhone(db, req.Phone)
	if err != nil {
		logs.WithError(err).Error("Failed to get user by phone")
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	if user == nil {
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.NewBizErrf(constant.ErrAuth, "Invalid username or password")))
		return
	}

	isValid, err := service.ValidatePassword(c, user.UUID.String(), req.Password, user.Password)
	if err != nil {
		logs.WithError(err).Error("Failed to validate password")
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}
	if !isValid {
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.NewBizErrf(constant.ErrAuth, "Invalid username or password")))
		return
	}

	// Generate token
	token := utils.GenerateUserToken(c, user.UUID.String())

	// Save token in Redis
	redisClient := rclient.GetRedisClient()
	tokenKey := rclient.GetUserTokenKey(constant.AuthTokenKeyPrefix, token.Token)
	if err := redisClient.SetEx(c, tokenKey, user.UUID.String(), utils.MaxUserTokenExpTime).Err(); err != nil {
		logs.WithError(err).Errorf("fail to save key %s", tokenKey)
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	response := LoginByPhoneAndPasswordResponse{
		AccessToken: token.Token,
		ExpireTime:  token.ExpiredAt,
	}
	c.JSON(http.StatusOK, commonModel.NewSuccessResponse(response))
}

type RegisterRequest struct {
	Phone    string `json:"phone" binding:"required"`
	Password string `json:"password" binding:"required"`
}

// RegisterHandler handles user registration
func RegisterHandler(c *gin.Context) {
	logs := logger.GetLogger(c)
	var req RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrParam, err)))
		return
	}

	db := gorm.GetMultiDB("default")

	// Check if phone is provided and already exists
	if req.Phone != "" {
		existingUser, err := model.GetUserWithPhone(db, req.Phone)
		if err != nil {
			logs.WithError(err).Error("Failed to check existing phone")
			c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrDB, err)))
			return
		}
		if existingUser != nil {
			c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.NewBizErrf(constant.ErrParam, "Phone number already exists")))
			return
		}
	}

	// Hash password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.Password), bcrypt.DefaultCost)
	if err != nil {
		logs.WithError(err).Error("Failed to hash password")
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrUnknown, err)))
		return
	}

	// Create new user
	newUser := &model.User{
		Phone:    req.Phone,
		Password: string(hashedPassword),
	}

	if err := model.CreateUser(db, newUser); err != nil {
		logs.WithError(err).Error("Failed to create user")
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	c.JSON(http.StatusOK, commonModel.NewSuccessResponse(nil))
}

func LogoutHandler(c *gin.Context) {
	logs := logger.GetLogger(c)
	token := commonUtils.GetCtxAuthToken(c)
	if token == nil {
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.NewBizErrf(constant.ErrAuth, "Unauthorized")))
		return
	}

	key := rclient.GetUserTokenKey(constant.AuthTokenKeyPrefix, token.Token)
	redisClient := rclient.GetRedisClient()
	if err := redisClient.Del(c, key).Err(); err != nil {
		logs.WithError(err).Errorf("fail to delete key %s", key)
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	c.JSON(http.StatusOK, commonModel.NewSuccessResponse(nil))
}

type SendSMSRequest struct {
	Phone string `json:"phone"`
}

func SendSMS(ginCtx *gin.Context) {
	var req SendSMSRequest
	if err := ginCtx.Bind(&req); err != nil {
		ginCtx.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrParam, err)))
		return
	}

	if _, err := service.SendValidaionCode(ginCtx, req.Phone); err != nil {
		ginCtx.JSON(http.StatusOK, commonModel.NewErrorResponse(err))
		return
	}

	ginCtx.JSON(http.StatusOK, commonModel.NewSuccessResponse(nil))
}

type SMSAuthRequest struct {
	Phone string `json:"phone"`
	Code  string `json:"code"`
}

func SMSAuth(ginCtx *gin.Context) {
	logs := logger.GetLogger(ginCtx)
	var req SMSAuthRequest
	if err := ginCtx.Bind(&req); err != nil {
		ginCtx.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrParam, err)))
		return
	}
	db := gorm.GetMultiDB("")

	if _, err := service.ValidatePhoneCode(ginCtx, req.Phone, req.Code); err != nil {
		ginCtx.JSON(http.StatusOK, commonModel.NewErrorResponse(err))
		return
	}
	// 查询或初始化人员
	user, err := model.GetUserWithPhone(db, req.Phone)
	if err != nil || user == nil {
		if err == nil {
			err = errors.New("nil user")
		}
		logs.WithError(err).Error("fail to get user")
		ginCtx.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	// 生成登录态
	token := utils.GenerateUserToken(ginCtx, user.UUID.String())

	response := LoginByPhoneAndPasswordResponse{
		AccessToken: token.Token,
		ExpireTime:  token.ExpiredAt,
	}
	ginCtx.JSON(http.StatusOK, commonModel.NewSuccessResponse(response))
}
