package controller

import (
	"net/http"

	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/management-svc/constant"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/management-svc/service"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/model"
	"codeup.aliyun.com/level-up/public/common/bizerr"
	gormClient "codeup.aliyun.com/level-up/public/common/clients/gorm"
	"codeup.aliyun.com/level-up/public/common/logger"
	commonModel "codeup.aliyun.com/level-up/public/common/model"
	commonUtils "codeup.aliyun.com/level-up/public/common/utils"
	"github.com/gin-gonic/gin"
)

type GetTenantDetailRequest struct {
	TenantID string `form:"tenantId" binding:"required"`
}

type GetTenantDetailResponse struct {
	TenantID    string `json:"tenantId"`
	TenantName  string `json:"tenantName"`
	TenantPoint int64  `json:"tenantPoint"`
}

func GetTenantDetail(c *gin.Context) {
	logs := logger.GetLogger(c)
	token := commonUtils.GetCtxAuthToken(c)
	if token == nil {
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.NewBizErrf(constant.ErrAuth, "Unauthorized")))
		return
	}

	var req GetTenantDetailRequest
	if err := c.ShouldBindQuery(&req); err != nil {
		logs.WithError(err).Error("Failed to bind uri parameters")
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrParam, err)))
		return
	}
	if req.TenantID == "" {
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.NewBizErrf(constant.ErrParam, "Tenant ID is required")))
		return
	}

	db := gormClient.GetMultiDB("default")
	// check if user is admin
	isAdmin, err := service.CheckTenantUserRoleIsAdmin(db, req.TenantID, token.UserID)
	if err != nil {
		logs.WithError(err).Error("Failed to check tenant user role")
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	if !isAdmin {
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.NewBizErrf(constant.ErrAuth, "Unauthorized")))
		return
	}

	tenant, err := model.GetTenantDetailByID(db, req.TenantID)
	if err != nil {
		logs.WithError(err).Error("Failed to get tenant details")
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	if tenant == nil {
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.NewBizErrf(constant.ErrAuth, "Tenant not found")))
		return
	}

	response := GetTenantDetailResponse{
		TenantID:    tenant.TenantID,
		TenantName:  tenant.TenantName,
		TenantPoint: tenant.TenantPoint,
	}

	c.JSON(http.StatusOK, commonModel.NewSuccessResponse(response))
}
