package controller

import (
	"net/http"

	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/management-svc/constant"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/model"
	"codeup.aliyun.com/level-up/public/common/bizerr"
	"codeup.aliyun.com/level-up/public/common/clients/gorm"
	"codeup.aliyun.com/level-up/public/common/logger"
	commonModel "codeup.aliyun.com/level-up/public/common/model"
	commonUtils "codeup.aliyun.com/level-up/public/common/utils"
	"github.com/gin-gonic/gin"
	"golang.org/x/crypto/bcrypt"
)

type CurrentUserResponse struct {
	UserID   string `json:"userId"`
	Nickname string `json:"nickname"`
	Avatar   string `json:"avatar"`
}

func GetCurrentUser(c *gin.Context) {
	logs := logger.GetLogger(c)
	token := commonUtils.GetCtxAuthToken(c)
	if token == nil {
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.NewBizErrf(constant.ErrAuth, "Unauthorized")))
		return
	}

	db := gorm.GetMultiDB("default")
	user, err := model.GetUser(db, token.UserID)
	if err != nil {
		logs.WithError(err).Error("Failed to get user by ID")
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	if user == nil {
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.NewBizErrf(constant.ErrAuth, "User not found")))
		return
	}

	response := CurrentUserResponse{
		UserID:   user.UUID.String(),
		Nickname: user.Nickname,
		Avatar:   user.Avatar,
	}
	c.JSON(http.StatusOK, commonModel.NewSuccessResponse(response))
}

type UpdatePasswordRequest struct {
	NewPassword    string `json:"newPassword" binding:"required"`
	RepeatPassword string `json:"repeatPassword" binding:"required"`
}

// UpdatePasswordHandler handles password update for authenticated users
func UpdatePasswordHandler(c *gin.Context) {
	logs := logger.GetLogger(c)
	token := commonUtils.GetCtxAuthToken(c)
	if token == nil {
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.NewBizErrf(constant.ErrAuth, "Unauthorized")))
		return
	}

	var req UpdatePasswordRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrParam, err)))
		return
	}

	// Check if new password matches repeated password
	if req.NewPassword != req.RepeatPassword {
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.NewBizErrf(constant.ErrParam, "New password and repeat password don't match")))
		return
	}

	// Get user ID
	db := gorm.GetMultiDB("default")
	userID := token.UserID

	// Get user from database
	user, err := model.GetUser(db, userID)
	if err != nil {
		logs.WithError(err).Error("Failed to get user by ID")
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	if user == nil {
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.NewBizErrf(constant.ErrAuth, "User not found")))
		return
	}

	// Hash new password
	hashedPassword, err := bcrypt.GenerateFromPassword([]byte(req.NewPassword), bcrypt.DefaultCost)
	if err != nil {
		logs.WithError(err).Error("Failed to hash password")
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrUnknown, err)))
		return
	}

	// Update user password
	user.Password = string(hashedPassword)
	if err := model.UpdateUser(gorm.GetMultiDB("default"), user); err != nil {
		logs.WithError(err).Error("Failed to update user password")
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	c.JSON(http.StatusOK, commonModel.NewSuccessResponse(nil))
}

type GetCurrentUserAdminTenantsResponse struct {
	TenantID   string `json:"tenantId"`
	TenantName string `json:"tenantName"`
	RoleID     string `json:"roleId"`
	RoleName   string `json:"roleName"`
	RoleCode   string `json:"roleCode"`
}

func GetCurrentUserAdminTenants(c *gin.Context) {
	logs := logger.GetLogger(c)
	token := commonUtils.GetCtxAuthToken(c)
	if token == nil {
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.NewBizErrf(constant.ErrAuth, "Unauthorized")))
		return
	}

	db := gorm.GetMultiDB("default")
	tenants, err := model.GetTenantUserRoleByUserID(db, token.UserID)
	if err != nil {
		logs.WithError(err).Error("Failed to get tenant user roles")
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.WrapErr(constant.ErrDB, err)))
		return
	}

	if tenants == nil {
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.NewBizErrf(constant.ErrAuth, "User not found")))
		return
	}
	if len(tenants) == 0 {
		c.JSON(http.StatusOK, commonModel.NewErrorResponse(bizerr.NewBizErrf(constant.ErrAuth, "User not found")))
		return
	}
	// filter out non-admin roles
	var response []GetCurrentUserAdminTenantsResponse
	for _, tenant := range tenants {
		if tenant.RoleCode != model.TenantRoleAdmin {
			continue
		}
		response = append(response, GetCurrentUserAdminTenantsResponse{
			TenantID:   tenant.TenantID.String(),
			TenantName: tenant.TenantName,
			RoleID:     tenant.RoleID.String(),
			RoleName:   tenant.RoleName,
			RoleCode:   tenant.RoleCode,
		})
	}
	c.JSON(http.StatusOK, commonModel.NewSuccessResponse(response))
}
