package main

import (
	"context"
	"fmt"
	"sync"

	"codeup.aliyun.com/level-up/public/common/clients/aliyun"
	"codeup.aliyun.com/level-up/public/common/clients/gorm"

	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/management-svc/constant"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/app/management-svc/controller"
	"codeup.aliyun.com/level-up/correct-king/monorepo-backend/model"
	"codeup.aliyun.com/level-up/public/common/clients/redis"
	"codeup.aliyun.com/level-up/public/common/goroutine"
	"codeup.aliyun.com/level-up/public/common/logger"
	"codeup.aliyun.com/level-up/public/common/middleware/ginmw"
	"codeup.aliyun.com/level-up/public/common/utils"
	"github.com/gin-gonic/gin"
	"github.com/sirupsen/logrus"
	"github.com/spf13/viper"
)

var serviceRunMap = map[string]func(){
	"api": runApiService,
}

func Init() {
	utils.InitViper()
	{
		logger.Init(&logger.LogrusOption{
			Level: logrus.Level(viper.GetInt("logrus.level")),
		})
	}
	aliyun.InitSMS()
	redis.Init()
	gorm.InitMultiDB()
	model.Init(gorm.GetMultiDB(""))
}

func main() {
	Init()
	var wg sync.WaitGroup
	for _, name := range viper.GetStringSlice("service.enable") {
		f, ok := serviceRunMap[name]
		if !ok {
			continue
		}
		wg.Add(1)
		goroutine.Go(context.Background(), func() {
			defer wg.Done()
			f()
		})
	}
	wg.Wait()
}

func runApiService() {
	logs := logger.GetLogger(context.Background())
	r := gin.Default()
	r.Use(ginmw.LoggerMW)
	v1 := r.Group("/api/v1")
	authMW := ginmw.AuthMW(constant.AuthTokenKeyPrefix)
	{
		v1.POST("/auth/phone/sendCode", controller.SendSMS)
		v1.POST("/auth/phone/loginByPassword", controller.LoginByPhoneAndPasswordHandler)
		v1.POST("/auth/phone/loginByCode", controller.SMSAuth)
		v1.POST("/auth/register", controller.RegisterHandler)
	}

	{
		v1.GET("/auth/current", authMW, controller.GetCurrentUser)
		v1.POST("/auth/logout", authMW, controller.LogoutHandler)
	}

	{
		my := v1.Group("/my", authMW)
		my.GET("/tenants", controller.GetCurrentUserAdminTenants)
		my.POST("/password", controller.UpdatePasswordHandler)
	}

	{
		tenants := v1.Group("/tenants", authMW)
		tenants.GET("/detail", controller.GetTenantDetail)
		tenants.GET("/users", controller.ListTenantUser)
		tenants.POST("/users/status", controller.UpdateTenantUserStatus)
		tenants.DELETE("/users", controller.DeleteTenantUser)
	}

	if err := r.Run(fmt.Sprintf(":%d", viper.GetInt("service.api.port"))); err != nil {
		logs.WithError(err).Errorf("api service start failed")
	}
}
